(function(t){function e(e){for(var a,c,o=e[0],n=e[1],l=e[2],u=0,p=[];u<o.length;u++)c=o[u],Object.prototype.hasOwnProperty.call(r,c)&&r[c]&&p.push(r[c][0]),r[c]=0;for(a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a]);h&&h(e);while(p.length)p.shift()();return i.push.apply(i,l||[]),s()}function s(){for(var t,e=0;e<i.length;e++){for(var s=i[e],a=!0,o=1;o<s.length;o++){var n=s[o];0!==r[n]&&(a=!1)}a&&(i.splice(e--,1),t=c(c.s=s[0]))}return t}var a={},r={app:0},i=[];function c(e){if(a[e])return a[e].exports;var s=a[e]={i:e,l:!1,exports:{}};return t[e].call(s.exports,s,s.exports,c),s.l=!0,s.exports}c.m=t,c.c=a,c.d=function(t,e,s){c.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:s})},c.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},c.t=function(t,e){if(1&e&&(t=c(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var s=Object.create(null);if(c.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)c.d(s,a,function(e){return t[e]}.bind(null,a));return s},c.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return c.d(e,"a",e),e},c.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},c.p="/";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],n=o.push.bind(o);o.push=e,o=o.slice();for(var l=0;l<o.length;l++)e(o[l]);var h=n;i.push([0,"chunk-vendors"]),s()})({0:function(t,e,s){t.exports=s("56d7")},"1b73":function(t,e,s){"use strict";s("fc61")},"56d7":function(t,e,s){"use strict";s.r(e);var a=s("2b0e"),r=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"app"}},[e("router-view")],1)},i=[],c={name:"App"},o=c,n=(s("1b73"),s("2877")),l=Object(n["a"])(o,r,i,!1,null,null,null),h=l.exports,u=s("8c4f"),p=function(){var t=this,e=t._self._c;return e("div",{staticClass:"comic-list"},[e("el-header",{staticClass:"header"},[e("div",{staticClass:"header-content"},[e("div",{staticClass:"logo"},[e("h2",[t._v("漫画网站")])]),e("div",{staticClass:"search-box"},[e("el-input",{attrs:{placeholder:"搜索漫画...","prefix-icon":"el-icon-search",clearable:""},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchComics.apply(null,arguments)}},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}}),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchComics}},[t._v("搜索")])],1),e("div",{staticClass:"user-actions"},[e("el-button",{attrs:{type:"text"}},[t._v("登录")]),e("el-button",{attrs:{type:"primary"}},[t._v("注册")])],1)])]),e("el-main",{staticClass:"main-content"},[e("div",{staticClass:"content-wrapper"},[e("div",{staticClass:"comic-grid"},t._l(t.filteredComics,(function(s){return e("div",{key:s.id,staticClass:"comic-card",on:{click:function(e){return t.goToDetail(s.id)}}},[e("div",{staticClass:"comic-cover"},[e("img",{attrs:{src:s.cover,alt:s.title}}),e("div",{staticClass:"comic-overlay"},[e("el-button",{attrs:{type:"primary",size:"small"}},[t._v("查看详情")])],1)]),e("div",{staticClass:"comic-info"},[e("h3",{staticClass:"comic-title"},[t._v(t._s(s.title))]),e("p",{staticClass:"comic-latest"},[t._v("最新: "+t._s(s.latestChapter))]),e("p",{staticClass:"comic-update"},[t._v(t._s(s.updateTime))])])])})),0),e("div",{staticClass:"pagination-wrapper"},[e("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":[12,24,36,48],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.totalFilteredComics},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])])],1)},d=[],m=(s("14d9"),s("e9f5"),s("910d"),{name:"ComicList",data(){return{searchKeyword:"",currentPage:1,pageSize:12,comics:[{id:1,title:"进击的巨人",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center",latestChapter:"第139话",updateTime:"2024-01-15"},{id:2,title:"鬼灭之刃",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240",latestChapter:"第205话",updateTime:"2024-01-14"},{id:3,title:"海贼王",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30",latestChapter:"第1100话",updateTime:"2024-01-13"},{id:4,title:"火影忍者",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270",latestChapter:"第700话",updateTime:"2024-01-12"},{id:5,title:"龙珠",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0",latestChapter:"第519话",updateTime:"2024-01-11"},{id:6,title:"死神",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180",latestChapter:"第686话",updateTime:"2024-01-10"},{id:7,title:"我的英雄学院",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120",latestChapter:"第390话",updateTime:"2024-01-09"},{id:8,title:"东京喰种",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300",latestChapter:"第179话",updateTime:"2024-01-08"},{id:9,title:"约定的梦幻岛",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60",latestChapter:"第181话",updateTime:"2024-01-07"},{id:10,title:"咒术回战",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210",latestChapter:"第245话",updateTime:"2024-01-06"},{id:11,title:"链锯人",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330",latestChapter:"第150话",updateTime:"2024-01-05"},{id:12,title:"间谍过家家",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90",latestChapter:"第95话",updateTime:"2024-01-04"}]}},computed:{filteredComics(){let t=this.comics;this.searchKeyword&&(t=this.comics.filter(t=>t.title.toLowerCase().includes(this.searchKeyword.toLowerCase())));const e=(this.currentPage-1)*this.pageSize,s=e+this.pageSize;return t.slice(e,s)},totalFilteredComics(){let t=this.comics;return this.searchKeyword&&(t=this.comics.filter(t=>t.title.toLowerCase().includes(this.searchKeyword.toLowerCase()))),t.length}},methods:{searchComics(){this.currentPage=1},goToDetail(t){this.$router.push({name:"ComicDetail",params:{id:t}})},handleSizeChange(t){this.pageSize=t,this.currentPage=1},handleCurrentChange(t){this.currentPage=t}}}),f=m,g=(s("d187"),Object(n["a"])(f,p,d,!1,null,"686e526f",null)),v=g.exports,C=function(){var t=this,e=t._self._c;return e("div",{staticClass:"comic-detail"},[e("el-header",{staticClass:"header"},[e("div",{staticClass:"header-content"},[e("el-button",{staticClass:"back-btn",attrs:{icon:"el-icon-arrow-left",type:"text"},on:{click:t.goBack}},[t._v(" 返回列表 ")]),e("h2",[t._v(t._s(t.comic.title))])],1)]),e("el-main",{staticClass:"main-content"},[e("div",{staticClass:"content-wrapper"},[e("div",{staticClass:"comic-info-section"},[e("div",{staticClass:"comic-cover-large"},[e("img",{attrs:{src:t.comic.cover,alt:t.comic.title}})]),e("div",{staticClass:"comic-details"},[e("h1",{staticClass:"comic-title"},[t._v(t._s(t.comic.title))]),e("div",{staticClass:"comic-meta"},[e("p",[e("strong",[t._v("作者:")]),t._v(" "+t._s(t.comic.author))]),e("p",[e("strong",[t._v("状态:")]),e("el-tag",{attrs:{type:"连载中"===t.comic.status?"success":"info"}},[t._v(" "+t._s(t.comic.status)+" ")])],1),e("p",[e("strong",[t._v("最新章节:")]),t._v(" "+t._s(t.comic.latestChapter))]),e("p",[e("strong",[t._v("更新时间:")]),t._v(" "+t._s(t.comic.updateTime))])]),e("div",{staticClass:"comic-description"},[e("h3",[t._v("简介")]),e("p",[t._v(t._s(t.comic.description))])]),e("div",{staticClass:"action-buttons"},[e("el-button",{attrs:{type:"primary",size:"large"},on:{click:t.startReading}},[t._v(" 开始阅读 ")]),e("el-button",{attrs:{size:"large"}},[t._v("收藏")])],1)])]),e("div",{staticClass:"chapters-section"},[e("div",{staticClass:"section-header"},[e("h3",[t._v("章节列表")]),e("div",{staticClass:"sort-controls"},[e("el-radio-group",{on:{change:t.sortChapters},model:{value:t.sortOrder,callback:function(e){t.sortOrder=e},expression:"sortOrder"}},[e("el-radio-button",{attrs:{label:"asc"}},[t._v("正序")]),e("el-radio-button",{attrs:{label:"desc"}},[t._v("倒序")])],1)],1)]),e("div",{staticClass:"chapters-grid"},t._l(t.sortedChapters,(function(s){return e("div",{key:s.id,staticClass:"chapter-item",on:{click:function(e){return t.readChapter(s.id)}}},[e("div",{staticClass:"chapter-number"},[t._v(t._s(s.number))]),e("div",{staticClass:"chapter-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"chapter-date"},[t._v(t._s(s.date))])])})),0)])])])],1)},b=[],_=(s("ab43"),{name:"ComicDetail",props:["id"],data(){return{sortOrder:"desc",comic:{id:1,title:"进击的巨人",author:"諫山創",status:"已完结",cover:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center",latestChapter:"第139话",updateTime:"2024-01-15",description:"《进击的巨人》是日本漫画家諫山創创作的漫画作品。故事讲述了人类与巨人之间的战斗，以及主人公艾伦·耶格尔等人为了人类的自由而奋斗的故事。作品以其独特的世界观、复杂的剧情和深刻的主题而广受好评。这部作品不仅在日本国内获得了巨大成功，在全球范围内也拥有大量粉丝，被誉为21世纪最优秀的漫画作品之一。"},chapters:[{id:1,number:"第1话",title:"致两千年后的你",date:"2023-01-01"},{id:2,number:"第2话",title:"那一天",date:"2023-01-02"},{id:3,number:"第3话",title:"解散式之夜",date:"2023-01-03"},{id:4,number:"第4话",title:"初阵",date:"2023-01-04"},{id:5,number:"第5话",title:"心脏的跳动声",date:"2023-01-05"},{id:6,number:"第6话",title:"少女所见的世界",date:"2023-01-06"},{id:7,number:"第7话",title:"小小的刀刃",date:"2023-01-07"},{id:8,number:"第8话",title:"咆哮",date:"2023-01-08"},{id:9,number:"第9话",title:"左臂的去向",date:"2023-01-09"},{id:10,number:"第10话",title:"应对",date:"2023-01-10"}]}},computed:{sortedChapters(){const t=[...this.chapters];return"asc"===this.sortOrder?t.sort((t,e)=>t.id-e.id):t.sort((t,e)=>e.id-t.id)}},methods:{goBack(){this.$router.push({name:"ComicList"})},startReading(){const t="asc"===this.sortOrder?Math.min(...this.chapters.map(t=>t.id)):Math.max(...this.chapters.map(t=>t.id));this.readChapter(t)},readChapter(t){this.$router.push({name:"ComicReader",params:{id:this.id,chapterId:t}})},sortChapters(){}},mounted(){console.log("Comic ID:",this.id)}}),w=_,y=(s("7b16"),Object(n["a"])(w,C,b,!1,null,"239f2f50",null)),k=y.exports,x=function(){var t=this,e=t._self._c;return e("div",{staticClass:"comic-reader",class:{fullscreen:t.isFullscreen}},[e("div",{staticClass:"top-controls",class:{hidden:t.controlsHidden}},[e("div",{staticClass:"controls-left"},[e("el-button",{staticClass:"control-btn",attrs:{icon:"el-icon-arrow-left",type:"text"},on:{click:t.goBack}},[t._v(" 返回 ")]),e("span",{staticClass:"chapter-info"},[t._v(t._s(t.currentChapter.title))])],1),e("div",{staticClass:"controls-right"},[e("el-button-group",{staticClass:"reading-mode-toggle"},[e("el-button",{attrs:{type:"horizontal"===t.readingMode?"primary":"",size:"small"},on:{click:function(e){return t.setReadingMode("horizontal")}}},[t._v(" 左右滑动 ")]),e("el-button",{attrs:{type:"vertical"===t.readingMode?"primary":"",size:"small"},on:{click:function(e){return t.setReadingMode("vertical")}}},[t._v(" 上下拼接 ")])],1),e("el-button",{staticClass:"control-btn",attrs:{icon:t.isFullscreen?"el-icon-copy-document":"el-icon-full-screen",type:"text"},on:{click:t.toggleFullscreen}},[t._v(" "+t._s(t.isFullscreen?"退出全屏":"全屏")+" ")])],1)]),e("div",{staticClass:"reading-area",on:{click:t.toggleControls,touchstart:t.handleTouchStart,touchend:t.handleTouchEnd,touchmove:t.handleTouchMove}},["horizontal"===t.readingMode?e("div",{staticClass:"horizontal-reader"},[e("div",{ref:"imageContainer",staticClass:"image-container"},t._l(t.currentChapter.images,(function(s,a){return e("img",{key:a,staticClass:"comic-image",class:{active:a===t.currentPage},attrs:{src:s,alt:`第${t.currentPage+1}页`},on:{load:t.onImageLoad,error:t.onImageError}})})),0),e("div",{staticClass:"nav-buttons"},[t.currentPage>0?e("el-button",{staticClass:"nav-btn nav-btn-left",attrs:{icon:"el-icon-arrow-left",circle:""},on:{click:function(e){return e.stopPropagation(),t.prevPage.apply(null,arguments)}}}):t._e(),t.currentPage<t.currentChapter.images.length-1?e("el-button",{staticClass:"nav-btn nav-btn-right",attrs:{icon:"el-icon-arrow-right",circle:""},on:{click:function(e){return e.stopPropagation(),t.nextPage.apply(null,arguments)}}}):t._e()],1)]):e("div",{ref:"verticalReader",staticClass:"vertical-reader"},t._l(t.currentChapter.images,(function(s,a){return e("img",{key:a,staticClass:"comic-image-vertical",attrs:{src:s,alt:`第${a+1}页`},on:{load:t.onImageLoad,error:t.onImageError}})})),0)]),e("div",{staticClass:"bottom-controls",class:{hidden:t.controlsHidden}},["horizontal"===t.readingMode?e("div",{staticClass:"page-info"},[e("span",[t._v(t._s(t.currentPage+1)+" / "+t._s(t.currentChapter.images.length))])]):t._e(),e("div",{staticClass:"chapter-navigation"},[e("el-button",{attrs:{disabled:!t.hasPrevChapter,size:"small"},on:{click:t.prevChapter}},[t._v(" 上一章 ")]),e("el-select",{staticStyle:{width:"200px"},attrs:{size:"small"},on:{change:t.changeChapter},model:{value:t.currentChapterId,callback:function(e){t.currentChapterId=e},expression:"currentChapterId"}},t._l(t.allChapters,(function(t){return e("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})})),1),e("el-button",{attrs:{disabled:!t.hasNextChapter,size:"small"},on:{click:t.nextChapter}},[t._v(" 下一章 ")])],1),e("div",{staticClass:"quick-actions"},[e("el-button",{attrs:{size:"small"},on:{click:t.goHome}},[t._v("回到首页")])],1)]),t.loading?e("div",{staticClass:"loading-overlay"},[e("el-loading-text",[t._v("加载中...")])],1):t._e()])},P=[],F=(s("f665"),{name:"ComicReader",props:["id","chapterId"],data(){return{readingMode:"vertical",currentPage:0,currentChapterId:parseInt(this.chapterId),isFullscreen:!1,controlsHidden:!1,loading:!1,touchStartX:0,touchStartY:0,touchStartTime:0,currentChapter:{id:1,title:"第1话 致两千年后的你",images:["https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300"]},allChapters:[{id:1,title:"第1话 致两千年后的你"},{id:2,title:"第2话 那一天"},{id:3,title:"第3话 解散式之夜"},{id:4,title:"第4话 初阵"},{id:5,title:"第5话 心脏的跳动声"}]}},computed:{hasPrevChapter(){const t=this.allChapters.findIndex(t=>t.id===this.currentChapterId);return t>0},hasNextChapter(){const t=this.allChapters.findIndex(t=>t.id===this.currentChapterId);return t<this.allChapters.length-1}},methods:{setReadingMode(t){this.readingMode=t,"horizontal"===t&&(this.currentPage=0),localStorage.setItem("readingMode",t)},toggleControls(){this.controlsHidden=!this.controlsHidden},toggleFullscreen(){this.isFullscreen?this.exitFullscreen():this.enterFullscreen()},enterFullscreen(){const t=this.$el;t.requestFullscreen?t.requestFullscreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.msRequestFullscreen&&t.msRequestFullscreen(),this.isFullscreen=!0},exitFullscreen(){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen(),this.isFullscreen=!1},prevPage(){this.currentPage>0&&(this.currentPage--,this.updateImageDisplay())},nextPage(){this.currentPage<this.currentChapter.images.length-1?(this.currentPage++,this.updateImageDisplay()):this.hasNextChapter&&this.nextChapter()},updateImageDisplay(){if("horizontal"===this.readingMode){const t=this.$refs.imageContainer;if(t){const e=t.clientWidth;t.scrollLeft=this.currentPage*e}}},prevChapter(){if(this.hasPrevChapter){const t=this.allChapters.findIndex(t=>t.id===this.currentChapterId),e=this.allChapters[t-1];this.changeChapter(e.id)}},nextChapter(){if(this.hasNextChapter){const t=this.allChapters.findIndex(t=>t.id===this.currentChapterId),e=this.allChapters[t+1];this.changeChapter(e.id)}},changeChapter(t){this.loading=!0,this.currentChapterId=t,this.currentPage=0,setTimeout(()=>{const e=this.allChapters.find(e=>e.id===t);this.currentChapter={...e,images:["https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+30*t,"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+(30*t+60),"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+(30*t+120),"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+(30*t+180),"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+(30*t+240),"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue="+(30*t+300)]},this.loading=!1,this.$router.replace({name:"ComicReader",params:{id:this.id,chapterId:t}})},1e3)},goBack(){this.$router.push({name:"ComicDetail",params:{id:this.id}})},goHome(){this.$router.push({name:"ComicList"})},onImageLoad(){},onImageError(){console.error("图片加载失败")},handleTouchStart(t){1===t.touches.length&&(this.touchStartX=t.touches[0].clientX,this.touchStartY=t.touches[0].clientY,this.touchStartTime=Date.now())},handleTouchMove(t){if("horizontal"===this.readingMode&&1===t.touches.length){const e=t.touches[0].clientX,s=t.touches[0].clientY,a=Math.abs(e-this.touchStartX),r=Math.abs(s-this.touchStartY);a>r&&t.preventDefault()}},handleTouchEnd(t){if(!this.touchStartX||1!==t.changedTouches.length)return;const e=t.changedTouches[0].clientX,s=t.changedTouches[0].clientY,a=Date.now(),r=this.touchStartX-e,i=this.touchStartY-s,c=a-this.touchStartTime,o=50,n=500,l=100;Math.abs(r)>o&&Math.abs(i)<l&&c<n?"horizontal"===this.readingMode&&(r>0?this.nextPage():this.prevPage()):Math.abs(r)<10&&Math.abs(i)<10&&c<300&&this.toggleControls(),this.touchStartX=0,this.touchStartY=0,this.touchStartTime=0},handleKeydown(t){if("horizontal"===this.readingMode)switch(t.key){case"ArrowLeft":t.preventDefault(),this.prevPage();break;case"ArrowRight":t.preventDefault(),this.nextPage();break;case" ":t.preventDefault(),this.nextPage();break;case"Escape":this.isFullscreen&&this.exitFullscreen();break}}},mounted(){const t=localStorage.getItem("readingMode");t&&(this.readingMode=t),document.addEventListener("keydown",this.handleKeydown),document.addEventListener("fullscreenchange",()=>{this.isFullscreen=!!document.fullscreenElement}),document.addEventListener("webkitfullscreenchange",()=>{this.isFullscreen=!!document.webkitFullscreenElement}),this.changeChapter(this.currentChapterId)},beforeDestroy(){document.removeEventListener("keydown",this.handleKeydown),document.removeEventListener("fullscreenchange",()=>{this.isFullscreen=!!document.fullscreenElement}),document.removeEventListener("webkitfullscreenchange",()=>{this.isFullscreen=!!document.webkitFullscreenElement})}}),S=F,z=(s("bb29"),Object(n["a"])(S,x,P,!1,null,"2a02587f",null)),T=z.exports;a["default"].use(u["a"]);const I=[{path:"/",name:"ComicList",component:v},{path:"/comic/:id",name:"ComicDetail",component:k,props:!0},{path:"/comic/:id/chapter/:chapterId",name:"ComicReader",component:T,props:!0}],M=new u["a"]({mode:"history",base:"/",routes:I});var O=M,E=s("5c96"),L=s.n(E);s("0fae");a["default"].config.productionTip=!1,a["default"].use(L.a),new a["default"]({router:O,render:t=>t(h)}).$mount("#app")},"7b16":function(t,e,s){"use strict";s("e22d")},bb29:function(t,e,s){"use strict";s("f6ef")},d187:function(t,e,s){"use strict";s("d235")},d235:function(t,e,s){},e22d:function(t,e,s){},f6ef:function(t,e,s){},fc61:function(t,e,s){}});
//# sourceMappingURL=app.899baac2.js.map