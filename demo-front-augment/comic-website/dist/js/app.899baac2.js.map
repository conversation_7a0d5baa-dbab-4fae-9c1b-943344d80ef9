{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?7bb6", "webpack:///./src/App.vue", "webpack:///src/App.vue", "webpack:///./src/App.vue?03b3", "webpack:///./src/App.vue?315a", "webpack:///./src/views/ComicList.vue", "webpack:///src/views/ComicList.vue", "webpack:///./src/views/ComicList.vue?a5df", "webpack:///./src/views/ComicList.vue?17eb", "webpack:///./src/views/ComicDetail.vue", "webpack:///src/views/ComicDetail.vue", "webpack:///./src/views/ComicDetail.vue?176d", "webpack:///./src/views/ComicDetail.vue?ee4d", "webpack:///./src/views/ComicReader.vue", "webpack:///src/views/ComicReader.vue", "webpack:///./src/views/ComicReader.vue?6622", "webpack:///./src/views/ComicReader.vue?5a9f", "webpack:///./src/router/index.js", "webpack:///./src/main.js", "webpack:///./src/views/ComicDetail.vue?3a87", "webpack:///./src/views/ComicReader.vue?634c", "webpack:///./src/views/ComicList.vue?7f9a"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "component", "staticClass", "_v", "on", "$event", "type", "indexOf", "_k", "keyCode", "searchComics", "arguments", "model", "searchKeyword", "callback", "$$v", "expression", "_l", "filteredComics", "comic", "id", "goToDetail", "cover", "title", "_s", "latestChapter", "updateTime", "currentPage", "pageSize", "totalFilteredComics", "handleSizeChange", "handleCurrentChange", "comics", "computed", "filtered", "filter", "toLowerCase", "includes", "start", "end", "methods", "comicId", "$router", "params", "val", "goBack", "author", "status", "description", "startReading", "sortChapters", "sortOrder", "sortedChapters", "chapter", "readChapter", "number", "date", "props", "chapters", "sort", "a", "b", "firstChapter", "Math", "min", "map", "max", "chapterId", "mounted", "console", "log", "class", "isFullscreen", "controlsHidden", "currentChapter", "readingMode", "setReadingMode", "toggleFullscreen", "toggleControls", "handleTouchStart", "handleTouchEnd", "handleTouchMove", "ref", "images", "image", "index", "onImageLoad", "onImageError", "stopPropagation", "prevPage", "_e", "nextPage", "hasPrevChapter", "prevChapter", "staticStyle", "changeChapter", "currentChapterId", "allChapters", "hasNextChapter", "nextChapter", "goHome", "loading", "parseInt", "touchStartX", "touchStartY", "touchStartTime", "currentIndex", "findIndex", "localStorage", "setItem", "exitFullscreen", "enterFullscreen", "element", "$el", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "document", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "updateImageDisplay", "container", "$refs", "imageContainer", "imageWidth", "clientWidth", "scrollLeft", "setTimeout", "find", "replace", "error", "event", "touches", "clientX", "clientY", "Date", "now", "touchX", "touchY", "deltaX", "abs", "deltaY", "preventDefault", "changedTouches", "touchEndX", "touchEndY", "touchEndTime", "deltaTime", "minSwipeDistance", "maxSwipeTime", "maxVerticalDistance", "handleKeydown", "savedMode", "getItem", "addEventListener", "fullscreenElement", "webkitFullscreenElement", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "ComicList", "ComicDetail", "ComicReader", "router", "base", "process", "config", "productionTip", "ElementUI", "h", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,IAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,W,2DCAIyC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,IAEjHG,EAAkB,GCKP,GACf/B,KAAA,OCR6T,I,wBCQzTgC,EAAY,eACd,EACAP,EACAM,GACA,EACA,KACA,KACA,MAIa,EAAAC,E,oBCnBXP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,YAAY,CAACK,YAAY,UAAU,CAACL,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,MAAM,CAACK,YAAY,QAAQ,CAACL,EAAG,KAAK,CAACF,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,UAAU,cAAc,iBAAiB,UAAY,IAAIK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQZ,EAAIa,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOrB,IAAI,SAAgB,KAAYW,EAAIe,aAAa1D,MAAM,KAAM2D,aAAaC,MAAM,CAAClC,MAAOiB,EAAIkB,cAAeC,SAAS,SAAUC,GAAMpB,EAAIkB,cAAcE,GAAKC,WAAW,mBAAmBnB,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBK,GAAG,CAAC,MAAQT,EAAIe,eAAe,CAACf,EAAIQ,GAAG,SAAS,GAAGN,EAAG,MAAM,CAACK,YAAY,gBAAgB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,SAAS,CAACJ,EAAIQ,GAAG,QAAQN,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,YAAY,CAACJ,EAAIQ,GAAG,SAAS,OAAON,EAAG,UAAU,CAACK,YAAY,gBAAgB,CAACL,EAAG,MAAM,CAACK,YAAY,mBAAmB,CAACL,EAAG,MAAM,CAACK,YAAY,cAAcP,EAAIsB,GAAItB,EAAIuB,gBAAgB,SAASC,GAAO,OAAOtB,EAAG,MAAM,CAACb,IAAImC,EAAMC,GAAGlB,YAAY,aAAaE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAI0B,WAAWF,EAAMC,OAAO,CAACvB,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMoB,EAAMG,MAAM,IAAMH,EAAMI,SAAS1B,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACJ,EAAIQ,GAAG,WAAW,KAAKN,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,KAAK,CAACK,YAAY,eAAe,CAACP,EAAIQ,GAAGR,EAAI6B,GAAGL,EAAMI,UAAU1B,EAAG,IAAI,CAACK,YAAY,gBAAgB,CAACP,EAAIQ,GAAG,OAAOR,EAAI6B,GAAGL,EAAMM,kBAAkB5B,EAAG,IAAI,CAACK,YAAY,gBAAgB,CAACP,EAAIQ,GAAGR,EAAI6B,GAAGL,EAAMO,sBAAqB,GAAG7B,EAAG,MAAM,CAACK,YAAY,sBAAsB,CAACL,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeJ,EAAIgC,YAAY,aAAa,CAAC,GAAI,GAAI,GAAI,IAAI,YAAYhC,EAAIiC,SAAS,OAAS,0CAA0C,MAAQjC,EAAIkC,qBAAqBzB,GAAG,CAAC,cAAcT,EAAImC,iBAAiB,iBAAiBnC,EAAIoC,wBAAwB,QAAQ,IAEh/D/B,EAAkB,GCkEP,G,8BAAA,CACf/B,KAAA,YACApC,OACA,OACAgF,cAAA,GACAc,YAAA,EACAC,SAAA,GACAI,OAAA,CACA,CACAZ,GAAA,EACAG,MAAA,QACAD,MAAA,gGACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,OACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,MACAD,MAAA,gHACAG,cAAA,SACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,OACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,KACAD,MAAA,+GACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,KACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,SACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,OACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,EACAG,MAAA,SACAD,MAAA,gHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,GACAG,MAAA,OACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,GACAG,MAAA,MACAD,MAAA,iHACAG,cAAA,QACAC,WAAA,cAEA,CACAN,GAAA,GACAG,MAAA,QACAD,MAAA,gHACAG,cAAA,OACAC,WAAA,iBAKAO,SAAA,CACAf,iBACA,IAAAgB,EAAA,KAAAF,OACA,KAAAnB,gBACAqB,EAAA,KAAAF,OAAAG,OAAAhB,GACAA,EAAAI,MAAAa,cAAAC,SAAA,KAAAxB,cAAAuB,iBAGA,MAAAE,GAAA,KAAAX,YAAA,QAAAC,SACAW,EAAAD,EAAA,KAAAV,SACA,OAAAM,EAAAzC,MAAA6C,EAAAC,IAEAV,sBACA,IAAAK,EAAA,KAAAF,OAMA,OALA,KAAAnB,gBACAqB,EAAA,KAAAF,OAAAG,OAAAhB,GACAA,EAAAI,MAAAa,cAAAC,SAAA,KAAAxB,cAAAuB,iBAGAF,EAAA7F,SAGAmG,QAAA,CACA9B,eACA,KAAAiB,YAAA,GAEAN,WAAAoB,GACA,KAAAC,QAAA/F,KAAA,CAAAsB,KAAA,cAAA0E,OAAA,CAAAvB,GAAAqB,MAEAX,iBAAAc,GACA,KAAAhB,SAAAgB,EACA,KAAAjB,YAAA,GAEAI,oBAAAa,GACA,KAAAjB,YAAAiB,MCrMkV,ICQ9U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXlD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,gBAAgB,CAACL,EAAG,YAAY,CAACK,YAAY,UAAU,CAACL,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,YAAY,CAACK,YAAY,WAAWH,MAAM,CAAC,KAAO,qBAAqB,KAAO,QAAQK,GAAG,CAAC,MAAQT,EAAIkD,SAAS,CAAClD,EAAIQ,GAAG,YAAYN,EAAG,KAAK,CAACF,EAAIQ,GAAGR,EAAI6B,GAAG7B,EAAIwB,MAAMI,WAAW,KAAK1B,EAAG,UAAU,CAACK,YAAY,gBAAgB,CAACL,EAAG,MAAM,CAACK,YAAY,mBAAmB,CAACL,EAAG,MAAM,CAACK,YAAY,sBAAsB,CAACL,EAAG,MAAM,CAACK,YAAY,qBAAqB,CAACL,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMJ,EAAIwB,MAAMG,MAAM,IAAM3B,EAAIwB,MAAMI,WAAW1B,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,KAAK,CAACK,YAAY,eAAe,CAACP,EAAIQ,GAAGR,EAAI6B,GAAG7B,EAAIwB,MAAMI,UAAU1B,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,IAAI,CAACA,EAAG,SAAS,CAACF,EAAIQ,GAAG,SAASR,EAAIQ,GAAG,IAAIR,EAAI6B,GAAG7B,EAAIwB,MAAM2B,WAAWjD,EAAG,IAAI,CAACA,EAAG,SAAS,CAACF,EAAIQ,GAAG,SAASN,EAAG,SAAS,CAACE,MAAM,CAAC,KAA4B,QAArBJ,EAAIwB,MAAM4B,OAAmB,UAAY,SAAS,CAACpD,EAAIQ,GAAG,IAAIR,EAAI6B,GAAG7B,EAAIwB,MAAM4B,QAAQ,QAAQ,GAAGlD,EAAG,IAAI,CAACA,EAAG,SAAS,CAACF,EAAIQ,GAAG,WAAWR,EAAIQ,GAAG,IAAIR,EAAI6B,GAAG7B,EAAIwB,MAAMM,kBAAkB5B,EAAG,IAAI,CAACA,EAAG,SAAS,CAACF,EAAIQ,GAAG,WAAWR,EAAIQ,GAAG,IAAIR,EAAI6B,GAAG7B,EAAIwB,MAAMO,iBAAiB7B,EAAG,MAAM,CAACK,YAAY,qBAAqB,CAACL,EAAG,KAAK,CAACF,EAAIQ,GAAG,QAAQN,EAAG,IAAI,CAACF,EAAIQ,GAAGR,EAAI6B,GAAG7B,EAAIwB,MAAM6B,kBAAkBnD,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,SAASK,GAAG,CAAC,MAAQT,EAAIsD,eAAe,CAACtD,EAAIQ,GAAG,YAAYN,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIQ,GAAG,SAAS,OAAON,EAAG,MAAM,CAACK,YAAY,oBAAoB,CAACL,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,KAAK,CAACF,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,iBAAiB,CAACO,GAAG,CAAC,OAAST,EAAIuD,cAActC,MAAM,CAAClC,MAAOiB,EAAIwD,UAAWrC,SAAS,SAAUC,GAAMpB,EAAIwD,UAAUpC,GAAKC,WAAW,cAAc,CAACnB,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIQ,GAAG,QAAQN,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIQ,GAAG,SAAS,IAAI,KAAKN,EAAG,MAAM,CAACK,YAAY,iBAAiBP,EAAIsB,GAAItB,EAAIyD,gBAAgB,SAASC,GAAS,OAAOxD,EAAG,MAAM,CAACb,IAAIqE,EAAQjC,GAAGlB,YAAY,eAAeE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAI2D,YAAYD,EAAQjC,OAAO,CAACvB,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACP,EAAIQ,GAAGR,EAAI6B,GAAG6B,EAAQE,WAAW1D,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACP,EAAIQ,GAAGR,EAAI6B,GAAG6B,EAAQ9B,UAAU1B,EAAG,MAAM,CAACK,YAAY,gBAAgB,CAACP,EAAIQ,GAAGR,EAAI6B,GAAG6B,EAAQG,cAAa,UAAU,IAE9zExD,EAAkB,GC0EP,G,UAAA,CACf/B,KAAA,cACAwF,MAAA,OACA5H,OACA,OACAsH,UAAA,OACAhC,MAAA,CACAC,GAAA,EACAG,MAAA,QACAuB,OAAA,MACAC,OAAA,MACAzB,MAAA,gGACAG,cAAA,QACAC,WAAA,aACAsB,YAAA,sJAEAU,SAAA,CACA,CAAAtC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,UAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,MAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,QAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,KAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,SAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,UAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,QAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,KAAAiC,KAAA,cACA,CAAApC,GAAA,EAAAmC,OAAA,MAAAhC,MAAA,QAAAiC,KAAA,cACA,CAAApC,GAAA,GAAAmC,OAAA,OAAAhC,MAAA,KAAAiC,KAAA,iBAIAvB,SAAA,CACAmB,iBACA,MAAAM,EAAA,SAAAA,UACA,mBAAAP,UACAO,EAAAC,KAAA,CAAAC,EAAAC,IAAAD,EAAAxC,GAAAyC,EAAAzC,IAEAsC,EAAAC,KAAA,CAAAC,EAAAC,MAAAzC,GAAAwC,EAAAxC,MAIAoB,QAAA,CACAK,SACA,KAAAH,QAAA/F,KAAA,CAAAsB,KAAA,eAEAgF,eAEA,MAAAa,EAAA,aAAAX,UACAY,KAAAC,OAAA,KAAAN,SAAAO,IAAAlG,KAAAqD,KACA2C,KAAAG,OAAA,KAAAR,SAAAO,IAAAlG,KAAAqD,KACA,KAAAkC,YAAAQ,IAEAR,YAAAa,GACA,KAAAzB,QAAA/F,KAAA,CACAsB,KAAA,cACA0E,OAAA,CACAvB,GAAA,KAAAA,GACA+C,gBAIAjB,kBAIAkB,UAEAC,QAAAC,IAAA,iBAAAlD,OC9IoV,ICQhV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBX1B,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,eAAeqE,MAAM,CAAE,WAAc5E,EAAI6E,eAAgB,CAAC3E,EAAG,MAAM,CAACK,YAAY,eAAeqE,MAAM,CAAE,OAAU5E,EAAI8E,iBAAkB,CAAC5E,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,YAAY,CAACK,YAAY,cAAcH,MAAM,CAAC,KAAO,qBAAqB,KAAO,QAAQK,GAAG,CAAC,MAAQT,EAAIkD,SAAS,CAAClD,EAAIQ,GAAG,UAAUN,EAAG,OAAO,CAACK,YAAY,gBAAgB,CAACP,EAAIQ,GAAGR,EAAI6B,GAAG7B,EAAI+E,eAAenD,WAAW,GAAG1B,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,kBAAkB,CAACK,YAAY,uBAAuB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,KAA2B,eAApBJ,EAAIgF,YAA+B,UAAY,GAAG,KAAO,SAASvE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIiF,eAAe,iBAAiB,CAACjF,EAAIQ,GAAG,YAAYN,EAAG,YAAY,CAACE,MAAM,CAAC,KAA2B,aAApBJ,EAAIgF,YAA6B,UAAY,GAAG,KAAO,SAASvE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIiF,eAAe,eAAe,CAACjF,EAAIQ,GAAG,aAAa,GAAGN,EAAG,YAAY,CAACK,YAAY,cAAcH,MAAM,CAAC,KAAOJ,EAAI6E,aAAe,wBAA0B,sBAAsB,KAAO,QAAQpE,GAAG,CAAC,MAAQT,EAAIkF,mBAAmB,CAAClF,EAAIQ,GAAG,IAAIR,EAAI6B,GAAG7B,EAAI6E,aAAe,OAAS,MAAM,QAAQ,KAAK3E,EAAG,MAAM,CAACK,YAAY,eAAeE,GAAG,CAAC,MAAQT,EAAImF,eAAe,WAAanF,EAAIoF,iBAAiB,SAAWpF,EAAIqF,eAAe,UAAYrF,EAAIsF,kBAAkB,CAAsB,eAApBtF,EAAIgF,YAA8B9E,EAAG,MAAM,CAACK,YAAY,qBAAqB,CAACL,EAAG,MAAM,CAACqF,IAAI,iBAAiBhF,YAAY,mBAAmBP,EAAIsB,GAAItB,EAAI+E,eAAeS,QAAQ,SAASC,EAAMC,GAAO,OAAOxF,EAAG,MAAM,CAACb,IAAIqG,EAAMnF,YAAY,cAAcqE,MAAM,CAAE,OAAUc,IAAU1F,EAAIgC,aAAc5B,MAAM,CAAC,IAAMqF,EAAM,IAAM,IAAIzF,EAAIgC,YAAc,MAAMvB,GAAG,CAAC,KAAOT,EAAI2F,YAAY,MAAQ3F,EAAI4F,mBAAkB,GAAG1F,EAAG,MAAM,CAACK,YAAY,eAAe,CAAEP,EAAIgC,YAAc,EAAG9B,EAAG,YAAY,CAACK,YAAY,uBAAuBH,MAAM,CAAC,KAAO,qBAAqB,OAAS,IAAIK,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOmF,kBAAyB7F,EAAI8F,SAASzI,MAAM,KAAM2D,eAAehB,EAAI+F,KAAM/F,EAAIgC,YAAchC,EAAI+E,eAAeS,OAAO9I,OAAS,EAAGwD,EAAG,YAAY,CAACK,YAAY,wBAAwBH,MAAM,CAAC,KAAO,sBAAsB,OAAS,IAAIK,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOmF,kBAAyB7F,EAAIgG,SAAS3I,MAAM,KAAM2D,eAAehB,EAAI+F,MAAM,KAAK7F,EAAG,MAAM,CAACqF,IAAI,iBAAiBhF,YAAY,mBAAmBP,EAAIsB,GAAItB,EAAI+E,eAAeS,QAAQ,SAASC,EAAMC,GAAO,OAAOxF,EAAG,MAAM,CAACb,IAAIqG,EAAMnF,YAAY,uBAAuBH,MAAM,CAAC,IAAMqF,EAAM,IAAM,IAAIC,EAAQ,MAAMjF,GAAG,CAAC,KAAOT,EAAI2F,YAAY,MAAQ3F,EAAI4F,mBAAkB,KAAK1F,EAAG,MAAM,CAACK,YAAY,kBAAkBqE,MAAM,CAAE,OAAU5E,EAAI8E,iBAAkB,CAAsB,eAApB9E,EAAIgF,YAA8B9E,EAAG,MAAM,CAACK,YAAY,aAAa,CAACL,EAAG,OAAO,CAACF,EAAIQ,GAAGR,EAAI6B,GAAG7B,EAAIgC,YAAc,GAAG,MAAMhC,EAAI6B,GAAG7B,EAAI+E,eAAeS,OAAO9I,aAAasD,EAAI+F,KAAK7F,EAAG,MAAM,CAACK,YAAY,sBAAsB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,UAAYJ,EAAIiG,eAAe,KAAO,SAASxF,GAAG,CAAC,MAAQT,EAAIkG,cAAc,CAAClG,EAAIQ,GAAG,WAAWN,EAAG,YAAY,CAACiG,YAAY,CAAC,MAAQ,SAAS/F,MAAM,CAAC,KAAO,SAASK,GAAG,CAAC,OAAST,EAAIoG,eAAenF,MAAM,CAAClC,MAAOiB,EAAIqG,iBAAkBlF,SAAS,SAAUC,GAAMpB,EAAIqG,iBAAiBjF,GAAKC,WAAW,qBAAqBrB,EAAIsB,GAAItB,EAAIsG,aAAa,SAAS5C,GAAS,OAAOxD,EAAG,YAAY,CAACb,IAAIqE,EAAQjC,GAAGrB,MAAM,CAAC,MAAQsD,EAAQ9B,MAAM,MAAQ8B,EAAQjC,SAAQ,GAAGvB,EAAG,YAAY,CAACE,MAAM,CAAC,UAAYJ,EAAIuG,eAAe,KAAO,SAAS9F,GAAG,CAAC,MAAQT,EAAIwG,cAAc,CAACxG,EAAIQ,GAAG,YAAY,GAAGN,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,SAASK,GAAG,CAAC,MAAQT,EAAIyG,SAAS,CAACzG,EAAIQ,GAAG,WAAW,KAAMR,EAAI0G,QAASxG,EAAG,MAAM,CAACK,YAAY,mBAAmB,CAACL,EAAG,kBAAkB,CAACF,EAAIQ,GAAG,aAAa,GAAGR,EAAI+F,QAE3pH1F,EAAkB,GCqIP,G,UAAA,CACf/B,KAAA,cACAwF,MAAA,mBACA5H,OACA,OACA8I,YAAA,WACAhD,YAAA,EACAqE,iBAAAM,SAAA,KAAAnC,WACAK,cAAA,EACAC,gBAAA,EACA4B,SAAA,EACAE,YAAA,EACAC,YAAA,EACAC,eAAA,EACA/B,eAAA,CACAtD,GAAA,EACAG,MAAA,cACA4D,OAAA,CACA,iGACA,kHACA,iHACA,kHACA,gHACA,kHACA,kHACA,oHAGAc,YAAA,CACA,CAAA7E,GAAA,EAAAG,MAAA,eACA,CAAAH,GAAA,EAAAG,MAAA,WACA,CAAAH,GAAA,EAAAG,MAAA,aACA,CAAAH,GAAA,EAAAG,MAAA,UACA,CAAAH,GAAA,EAAAG,MAAA,iBAIAU,SAAA,CACA2D,iBACA,MAAAc,EAAA,KAAAT,YAAAU,UAAA5I,KAAAqD,KAAA,KAAA4E,kBACA,OAAAU,EAAA,GAEAR,iBACA,MAAAQ,EAAA,KAAAT,YAAAU,UAAA5I,KAAAqD,KAAA,KAAA4E,kBACA,OAAAU,EAAA,KAAAT,YAAA5J,OAAA,IAGAmG,QAAA,CACAoC,eAAAhG,GACA,KAAA+F,YAAA/F,EACA,eAAAA,IACA,KAAA+C,YAAA,GAGAiF,aAAAC,QAAA,cAAAjI,IAEAkG,iBACA,KAAAL,gBAAA,KAAAA,gBAEAI,mBACA,KAAAL,aAGA,KAAAsC,iBAFA,KAAAC,mBAKAA,kBACA,MAAAC,EAAA,KAAAC,IACAD,EAAAE,kBACAF,EAAAE,oBACAF,EAAAG,wBACAH,EAAAG,0BACAH,EAAAI,qBACAJ,EAAAI,uBACAJ,EAAAK,qBACAL,EAAAK,sBAEA,KAAA7C,cAAA,GAEAsC,iBACAQ,SAAAR,eACAQ,SAAAR,iBACAQ,SAAAC,qBACAD,SAAAC,uBACAD,SAAAE,oBACAF,SAAAE,sBACAF,SAAAG,kBACAH,SAAAG,mBAEA,KAAAjD,cAAA,GAEAiB,WACA,KAAA9D,YAAA,IACA,KAAAA,cACA,KAAA+F,uBAGA/B,WACA,KAAAhE,YAAA,KAAA+C,eAAAS,OAAA9I,OAAA,GACA,KAAAsF,cACA,KAAA+F,sBAGA,KAAAxB,gBACA,KAAAC,eAIAuB,qBACA,uBAAA/C,YAAA,CACA,MAAAgD,EAAA,KAAAC,MAAAC,eACA,GAAAF,EAAA,CACA,MAAAG,EAAAH,EAAAI,YACAJ,EAAAK,WAAA,KAAArG,YAAAmG,KAIAjC,cACA,QAAAD,eAAA,CACA,MAAAc,EAAA,KAAAT,YAAAU,UAAA5I,KAAAqD,KAAA,KAAA4E,kBACAH,EAAA,KAAAI,YAAAS,EAAA,GACA,KAAAX,cAAAF,EAAAzE,MAGA+E,cACA,QAAAD,eAAA,CACA,MAAAQ,EAAA,KAAAT,YAAAU,UAAA5I,KAAAqD,KAAA,KAAA4E,kBACAG,EAAA,KAAAF,YAAAS,EAAA,GACA,KAAAX,cAAAI,EAAA/E,MAGA2E,cAAA5B,GACA,KAAAkC,SAAA,EACA,KAAAL,iBAAA7B,EACA,KAAAxC,YAAA,EAGAsG,WAAA,KACA,MAAA5E,EAAA,KAAA4C,YAAAiC,KAAAnK,KAAAqD,KAAA+C,GACA,KAAAO,eAAA,IACArB,EACA8B,OAAA,CACA,kHAAAhB,EACA,mHAAAA,EAAA,IACA,mHAAAA,EAAA,KACA,mHAAAA,EAAA,KACA,mHAAAA,EAAA,KACA,mHAAAA,EAAA,OAGA,KAAAkC,SAAA,EAGA,KAAA3D,QAAAyF,QAAA,CACAlK,KAAA,cACA0E,OAAA,CAAAvB,GAAA,KAAAA,GAAA+C,gBAEA,MAEAtB,SACA,KAAAH,QAAA/F,KAAA,CAAAsB,KAAA,cAAA0E,OAAA,CAAAvB,GAAA,KAAAA,OAEAgF,SACA,KAAA1D,QAAA/F,KAAA,CAAAsB,KAAA,eAEAqH,gBAGAC,eAEAlB,QAAA+D,MAAA,WAEArD,iBAAAsD,GACA,IAAAA,EAAAC,QAAAjM,SACA,KAAAkK,YAAA8B,EAAAC,QAAA,GAAAC,QACA,KAAA/B,YAAA6B,EAAAC,QAAA,GAAAE,QACA,KAAA/B,eAAAgC,KAAAC,QAGAzD,gBAAAoD,GAEA,uBAAA1D,aAAA,IAAA0D,EAAAC,QAAAjM,OAAA,CACA,MAAAsM,EAAAN,EAAAC,QAAA,GAAAC,QACAK,EAAAP,EAAAC,QAAA,GAAAE,QACAK,EAAA9E,KAAA+E,IAAAH,EAAA,KAAApC,aACAwC,EAAAhF,KAAA+E,IAAAF,EAAA,KAAApC,aAGAqC,EAAAE,GACAV,EAAAW,mBAIAhE,eAAAqD,GACA,SAAA9B,aAAA,IAAA8B,EAAAY,eAAA5M,OAAA,OAEA,MAAA6M,EAAAb,EAAAY,eAAA,GAAAV,QACAY,EAAAd,EAAAY,eAAA,GAAAT,QACAY,EAAAX,KAAAC,MAEAG,EAAA,KAAAtC,YAAA2C,EACAH,EAAA,KAAAvC,YAAA2C,EACAE,EAAAD,EAAA,KAAA3C,eAGA6C,EAAA,GACAC,EAAA,IACAC,EAAA,IAEAzF,KAAA+E,IAAAD,GAAAS,GACAvF,KAAA+E,IAAAC,GAAAS,GACAH,EAAAE,EAEA,oBAAA5E,cAEAkE,EAAA,EAEA,KAAAlD,WAGA,KAAAF,YAGA1B,KAAA+E,IAAAD,GAAA,IAAA9E,KAAA+E,IAAAC,GAAA,IAAAM,EAAA,KAEA,KAAAvE,iBAIA,KAAAyB,YAAA,EACA,KAAAC,YAAA,EACA,KAAAC,eAAA,GAEAgD,cAAApB,GACA,uBAAA1D,YACA,OAAA0D,EAAArJ,KACA,gBACAqJ,EAAAW,iBACA,KAAAvD,WACA,MACA,iBACA4C,EAAAW,iBACA,KAAArD,WACA,MACA,QACA0C,EAAAW,iBACA,KAAArD,WACA,MACA,aACA,KAAAnB,cACA,KAAAsC,iBAEA,SAKA1C,UAEA,MAAAsF,EAAA9C,aAAA+C,QAAA,eACAD,IACA,KAAA/E,YAAA+E,GAIApC,SAAAsC,iBAAA,eAAAH,eAGAnC,SAAAsC,iBAAA,wBACA,KAAApF,eAAA8C,SAAAuC,oBAIAvC,SAAAsC,iBAAA,8BACA,KAAApF,eAAA8C,SAAAwC,0BAIA,KAAA/D,cAAA,KAAAC,mBAEA+D,gBAEAzC,SAAA0C,oBAAA,eAAAP,eACAnC,SAAA0C,oBAAA,wBACA,KAAAxF,eAAA8C,SAAAuC,oBAEAvC,SAAA0C,oBAAA,8BACA,KAAAxF,eAAA8C,SAAAwC,6BCtaoV,ICQhV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCbfG,aAAIC,IAAIC,QAER,MAAMC,EAAS,CACb,CACEC,KAAM,IACNpM,KAAM,YACNgC,UAAWqK,GAEb,CACED,KAAM,aACNpM,KAAM,cACNgC,UAAWsK,EACX9G,OAAO,GAET,CACE4G,KAAM,gCACNpM,KAAM,cACNgC,UAAWuK,EACX/G,OAAO,IAILgH,EAAS,IAAIN,OAAU,CAC3BvL,KAAM,UACN8L,KAAMC,IACNP,WAGaK,Q,+BC5BfR,aAAIW,OAAOC,eAAgB,EAE3BZ,aAAIC,IAAIY,KAER,IAAIb,aAAI,CACNQ,SACA/K,OAAQqL,GAAKA,EAAEC,KACdC,OAAO,S,oCCbV,W,kCCAA,W,kCCAA,W", "file": "js/app.899baac2.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=76444f67&prod&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml {\n  /* 防止iOS Safari双击缩放 */\n  touch-action: manipulation;\n  /* 优化移动端滚动 */\n  -webkit-overflow-scrolling: touch;\n}\n\nbody {\n  background-color: #f5f5f5;\n  /* 防止移动端橡皮筋效果 */\n  overscroll-behavior: none;\n  /* 优化字体渲染 */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  /* 防止移动端文本选择 */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n#app {\n  font-family: 'Avenir', Helvetica, Arial, sans-serif, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n}\n\n/* 移动端输入框优化 */\ninput, textarea, select {\n  -webkit-user-select: text;\n  -moz-user-select: text;\n  -ms-user-select: text;\n  user-select: text;\n}\n\n/* Element UI 移动端优化 */\n.el-button {\n  /* 增加触摸区域 */\n  min-height: 36px;\n  /* 防止双击缩放 */\n  touch-action: manipulation;\n}\n\n.el-input__inner {\n  /* 防止iOS输入框缩放 */\n  font-size: 16px;\n  /* 优化移动端输入体验 */\n  -webkit-appearance: none;\n  appearance: none;\n  border-radius: 4px;\n}\n\n.el-select .el-input__inner {\n  cursor: pointer;\n}\n\n/* 分页组件移动端优化 */\n.el-pagination {\n  text-align: center;\n}\n\n.el-pagination .el-pager li {\n  min-width: 32px;\n  height: 32px;\n  line-height: 32px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .el-container {\n    flex-direction: column;\n  }\n\n  /* Element UI 组件移动端调整 */\n  .el-button {\n    min-height: 40px;\n    padding: 8px 15px;\n  }\n\n  .el-button--small {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-select .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n\n  /* 优化弹窗在移动端的显示 */\n  .el-dialog {\n    width: 90% !important;\n    margin: 5vh auto !important;\n  }\n\n  .el-message-box {\n    width: 90% !important;\n  }\n}\n\n@media (max-width: 480px) {\n  /* 超小屏幕优化 */\n  .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n    padding: 10px 15px;\n    font-size: 14px;\n  }\n\n  .el-button--small {\n    min-height: 40px;\n    padding: 8px 12px;\n    font-size: 13px;\n  }\n\n  .el-input__inner {\n    height: 44px;\n    line-height: 44px;\n    font-size: 16px; /* 防止iOS缩放 */\n  }\n\n  .el-pagination .el-pager li {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n    margin: 0 2px;\n  }\n\n  .el-pagination .btn-prev,\n  .el-pagination .btn-next {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  /* 横屏时减少垂直空间占用 */\n  .el-button {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 高分辨率屏幕优化 */\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n  /* 优化高分辨率屏幕的显示效果 */\n  #app {\n    -webkit-font-smoothing: subpixel-antialiased;\n  }\n}\n\n/* 暗色模式支持（预留） */\n@media (prefers-color-scheme: dark) {\n  /* 可以在这里添加暗色模式样式 */\n}\n\n/* 减少动画的用户偏好 */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=76444f67\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=76444f67&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"comic-list\"},[_c('el-header',{staticClass:\"header\"},[_c('div',{staticClass:\"header-content\"},[_c('div',{staticClass:\"logo\"},[_c('h2',[_vm._v(\"漫画网站\")])]),_c('div',{staticClass:\"search-box\"},[_c('el-input',{attrs:{\"placeholder\":\"搜索漫画...\",\"prefix-icon\":\"el-icon-search\",\"clearable\":\"\"},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchComics.apply(null, arguments)}},model:{value:(_vm.searchKeyword),callback:function ($$v) {_vm.searchKeyword=$$v},expression:\"searchKeyword\"}}),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchComics}},[_vm._v(\"搜索\")])],1),_c('div',{staticClass:\"user-actions\"},[_c('el-button',{attrs:{\"type\":\"text\"}},[_vm._v(\"登录\")]),_c('el-button',{attrs:{\"type\":\"primary\"}},[_vm._v(\"注册\")])],1)])]),_c('el-main',{staticClass:\"main-content\"},[_c('div',{staticClass:\"content-wrapper\"},[_c('div',{staticClass:\"comic-grid\"},_vm._l((_vm.filteredComics),function(comic){return _c('div',{key:comic.id,staticClass:\"comic-card\",on:{\"click\":function($event){return _vm.goToDetail(comic.id)}}},[_c('div',{staticClass:\"comic-cover\"},[_c('img',{attrs:{\"src\":comic.cover,\"alt\":comic.title}}),_c('div',{staticClass:\"comic-overlay\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\"查看详情\")])],1)]),_c('div',{staticClass:\"comic-info\"},[_c('h3',{staticClass:\"comic-title\"},[_vm._v(_vm._s(comic.title))]),_c('p',{staticClass:\"comic-latest\"},[_vm._v(\"最新: \"+_vm._s(comic.latestChapter))]),_c('p',{staticClass:\"comic-update\"},[_vm._v(_vm._s(comic.updateTime))])])])}),0),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.currentPage,\"page-sizes\":[12, 24, 36, 48],\"page-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.totalFilteredComics},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div\n            v-for=\"comic in filteredComics\"\n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalFilteredComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        },\n        {\n          id: 7,\n          title: '我的英雄学院',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',\n          latestChapter: '第390话',\n          updateTime: '2024-01-09'\n        },\n        {\n          id: 8,\n          title: '东京喰种',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',\n          latestChapter: '第179话',\n          updateTime: '2024-01-08'\n        },\n        {\n          id: 9,\n          title: '约定的梦幻岛',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',\n          latestChapter: '第181话',\n          updateTime: '2024-01-07'\n        },\n        {\n          id: 10,\n          title: '咒术回战',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',\n          latestChapter: '第245话',\n          updateTime: '2024-01-06'\n        },\n        {\n          id: 11,\n          title: '链锯人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',\n          latestChapter: '第150话',\n          updateTime: '2024-01-05'\n        },\n        {\n          id: 12,\n          title: '间谍过家家',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',\n          latestChapter: '第95话',\n          updateTime: '2024-01-04'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    },\n    totalFilteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      return filtered.length\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 10;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n  position: relative;\n  z-index: 10;\n}\n\n.search-box .el-input {\n  flex: 1;\n  position: relative;\n  z-index: 11;\n}\n\n.search-box .el-button {\n  position: relative;\n  z-index: 12;\n  flex-shrink: 0;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .header {\n    height: auto;\n    min-height: 70px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 15px;\n    gap: 15px;\n  }\n\n  .logo h2 {\n    font-size: 20px;\n    text-align: center;\n  }\n\n  .search-box {\n    margin: 0;\n    max-width: 100%;\n    width: 100%;\n    position: relative;\n    z-index: 20;\n    background: transparent;\n  }\n\n  .search-box .el-input {\n    font-size: 16px; /* 防止iOS缩放 */\n    position: relative;\n    z-index: 21;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: white !important;\n    border: 1px solid rgba(255, 255, 255, 0.5) !important;\n    color: #333 !important;\n    position: relative !important;\n    z-index: 1000 !important;\n  }\n\n  .search-box .el-input .el-input__inner::placeholder {\n    color: #999 !important;\n  }\n\n  .search-box .el-button {\n    position: relative !important;\n    z-index: 1001 !important;\n    background-color: rgba(255, 255, 255, 0.9) !important;\n    border-color: rgba(255, 255, 255, 0.9) !important;\n    color: #667eea !important;\n  }\n\n  .search-box .el-button:hover {\n    background-color: white !important;\n    border-color: white !important;\n    color: #667eea !important;\n  }\n\n  .user-actions {\n    justify-content: center;\n    width: 100%;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n    gap: 12px;\n  }\n\n  .comic-cover {\n    height: 200px;\n  }\n\n  .comic-info {\n    padding: 12px;\n  }\n\n  .comic-title {\n    font-size: 14px;\n    line-height: 1.3;\n  }\n\n  .comic-latest {\n    font-size: 13px;\n  }\n\n  .comic-update {\n    font-size: 11px;\n  }\n\n  .pagination-wrapper {\n    margin-top: 30px;\n  }\n\n  .pagination-wrapper .el-pagination {\n    text-align: center;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager li {\n    min-width: 32px;\n    height: 32px;\n    line-height: 32px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    padding: 12px;\n    gap: 12px;\n    position: relative;\n    z-index: 100;\n  }\n\n  .logo h2 {\n    font-size: 18px;\n  }\n\n  .search-box {\n    order: 2;\n    width: 100%;\n    margin: 0;\n    position: relative;\n    z-index: 101;\n  }\n\n  .search-box .el-input {\n    position: relative;\n    z-index: 102;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: white;\n    border: 1px solid #dcdfe6;\n    color: #333;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .search-box .el-button {\n    position: relative;\n    z-index: 103;\n    background-color: #409eff;\n    border-color: #409eff;\n    color: white;\n    height: 40px;\n    min-width: 60px;\n  }\n\n  .user-actions {\n    order: 3;\n    width: 100%;\n    justify-content: center;\n    position: relative;\n    z-index: 100;\n  }\n\n  .main-content {\n    padding: 12px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 10px;\n  }\n\n  .comic-cover {\n    height: 180px;\n  }\n\n  .comic-info {\n    padding: 10px;\n  }\n\n  .comic-title {\n    font-size: 13px;\n    margin-bottom: 6px;\n  }\n\n  .comic-latest {\n    font-size: 12px;\n    margin-bottom: 3px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n\n  .user-actions .el-button {\n    padding: 8px 15px;\n    font-size: 14px;\n  }\n\n  .search-box .el-button {\n    padding: 10px 15px;\n  }\n\n  /* 分页组件移动端优化 */\n  .pagination-wrapper .el-pagination {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__sizes {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__total {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .btn-prev,\n  .pagination-wrapper .el-pagination .btn-next {\n    margin: 0 2px 10px 2px;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__jump {\n    margin: 0 5px 10px 0;\n  }\n}\n\n/* 修复搜索框遮挡问题 */\n.search-box .el-input__inner {\n  position: relative !important;\n  z-index: 1000 !important;\n  background-color: white !important;\n}\n\n.search-box .el-button {\n  position: relative !important;\n  z-index: 1001 !important;\n}\n\n/* Element UI 下拉框层级修复 */\n.el-select-dropdown {\n  z-index: 999 !important;\n}\n\n.el-popper {\n  z-index: 999 !important;\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .header-content {\n    padding: 10px;\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n\n  .comic-grid {\n    gap: 8px;\n  }\n\n  .comic-cover {\n    height: 160px;\n  }\n\n  .comic-info {\n    padding: 8px;\n  }\n\n  .comic-title {\n    font-size: 12px;\n  }\n\n  .comic-latest {\n    font-size: 11px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicList.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicList.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ComicList.vue?vue&type=template&id=686e526f&scoped=true\"\nimport script from \"./ComicList.vue?vue&type=script&lang=js\"\nexport * from \"./ComicList.vue?vue&type=script&lang=js\"\nimport style0 from \"./ComicList.vue?vue&type=style&index=0&id=686e526f&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"686e526f\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"comic-detail\"},[_c('el-header',{staticClass:\"header\"},[_c('div',{staticClass:\"header-content\"},[_c('el-button',{staticClass:\"back-btn\",attrs:{\"icon\":\"el-icon-arrow-left\",\"type\":\"text\"},on:{\"click\":_vm.goBack}},[_vm._v(\" 返回列表 \")]),_c('h2',[_vm._v(_vm._s(_vm.comic.title))])],1)]),_c('el-main',{staticClass:\"main-content\"},[_c('div',{staticClass:\"content-wrapper\"},[_c('div',{staticClass:\"comic-info-section\"},[_c('div',{staticClass:\"comic-cover-large\"},[_c('img',{attrs:{\"src\":_vm.comic.cover,\"alt\":_vm.comic.title}})]),_c('div',{staticClass:\"comic-details\"},[_c('h1',{staticClass:\"comic-title\"},[_vm._v(_vm._s(_vm.comic.title))]),_c('div',{staticClass:\"comic-meta\"},[_c('p',[_c('strong',[_vm._v(\"作者:\")]),_vm._v(\" \"+_vm._s(_vm.comic.author))]),_c('p',[_c('strong',[_vm._v(\"状态:\")]),_c('el-tag',{attrs:{\"type\":_vm.comic.status === '连载中' ? 'success' : 'info'}},[_vm._v(\" \"+_vm._s(_vm.comic.status)+\" \")])],1),_c('p',[_c('strong',[_vm._v(\"最新章节:\")]),_vm._v(\" \"+_vm._s(_vm.comic.latestChapter))]),_c('p',[_c('strong',[_vm._v(\"更新时间:\")]),_vm._v(\" \"+_vm._s(_vm.comic.updateTime))])]),_c('div',{staticClass:\"comic-description\"},[_c('h3',[_vm._v(\"简介\")]),_c('p',[_vm._v(_vm._s(_vm.comic.description))])]),_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"large\"},on:{\"click\":_vm.startReading}},[_vm._v(\" 开始阅读 \")]),_c('el-button',{attrs:{\"size\":\"large\"}},[_vm._v(\"收藏\")])],1)])]),_c('div',{staticClass:\"chapters-section\"},[_c('div',{staticClass:\"section-header\"},[_c('h3',[_vm._v(\"章节列表\")]),_c('div',{staticClass:\"sort-controls\"},[_c('el-radio-group',{on:{\"change\":_vm.sortChapters},model:{value:(_vm.sortOrder),callback:function ($$v) {_vm.sortOrder=$$v},expression:\"sortOrder\"}},[_c('el-radio-button',{attrs:{\"label\":\"asc\"}},[_vm._v(\"正序\")]),_c('el-radio-button',{attrs:{\"label\":\"desc\"}},[_vm._v(\"倒序\")])],1)],1)]),_c('div',{staticClass:\"chapters-grid\"},_vm._l((_vm.sortedChapters),function(chapter){return _c('div',{key:chapter.id,staticClass:\"chapter-item\",on:{\"click\":function($event){return _vm.readChapter(chapter.id)}}},[_c('div',{staticClass:\"chapter-number\"},[_vm._v(_vm._s(chapter.number))]),_c('div',{staticClass:\"chapter-title\"},[_vm._v(_vm._s(chapter.title))]),_c('div',{staticClass:\"chapter-date\"},[_vm._v(_vm._s(chapter.date))])])}),0)])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"comic-detail\">\n    <!-- 顶部导航 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"back-btn\">\n          返回列表\n        </el-button>\n        <h2>{{ comic.title }}</h2>\n      </div>\n    </el-header>\n\n    <!-- 主要内容 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画信息区 -->\n        <div class=\"comic-info-section\">\n          <div class=\"comic-cover-large\">\n            <img :src=\"comic.cover\" :alt=\"comic.title\" />\n          </div>\n          <div class=\"comic-details\">\n            <h1 class=\"comic-title\">{{ comic.title }}</h1>\n            <div class=\"comic-meta\">\n              <p><strong>作者:</strong> {{ comic.author }}</p>\n              <p><strong>状态:</strong>\n                <el-tag :type=\"comic.status === '连载中' ? 'success' : 'info'\">\n                  {{ comic.status }}\n                </el-tag>\n              </p>\n              <p><strong>最新章节:</strong> {{ comic.latestChapter }}</p>\n              <p><strong>更新时间:</strong> {{ comic.updateTime }}</p>\n            </div>\n            <div class=\"comic-description\">\n              <h3>简介</h3>\n              <p>{{ comic.description }}</p>\n            </div>\n            <div class=\"action-buttons\">\n              <el-button type=\"primary\" size=\"large\" @click=\"startReading\">\n                开始阅读\n              </el-button>\n              <el-button size=\"large\">收藏</el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 章节列表区 -->\n        <div class=\"chapters-section\">\n          <div class=\"section-header\">\n            <h3>章节列表</h3>\n            <div class=\"sort-controls\">\n              <el-radio-group v-model=\"sortOrder\" @change=\"sortChapters\">\n                <el-radio-button label=\"asc\">正序</el-radio-button>\n                <el-radio-button label=\"desc\">倒序</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n\n          <div class=\"chapters-grid\">\n            <div\n              v-for=\"chapter in sortedChapters\"\n              :key=\"chapter.id\"\n              class=\"chapter-item\"\n              @click=\"readChapter(chapter.id)\"\n            >\n              <div class=\"chapter-number\">{{ chapter.number }}</div>\n              <div class=\"chapter-title\">{{ chapter.title }}</div>\n              <div class=\"chapter-date\">{{ chapter.date }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicDetail',\n  props: ['id'],\n  data() {\n    return {\n      sortOrder: 'desc',\n      comic: {\n        id: 1,\n        title: '进击的巨人',\n        author: '諫山創',\n        status: '已完结',\n        cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center',\n        latestChapter: '第139话',\n        updateTime: '2024-01-15',\n        description: '《进击的巨人》是日本漫画家諫山創创作的漫画作品。故事讲述了人类与巨人之间的战斗，以及主人公艾伦·耶格尔等人为了人类的自由而奋斗的故事。作品以其独特的世界观、复杂的剧情和深刻的主题而广受好评。这部作品不仅在日本国内获得了巨大成功，在全球范围内也拥有大量粉丝，被誉为21世纪最优秀的漫画作品之一。'\n      },\n      chapters: [\n        { id: 1, number: '第1话', title: '致两千年后的你', date: '2023-01-01' },\n        { id: 2, number: '第2话', title: '那一天', date: '2023-01-02' },\n        { id: 3, number: '第3话', title: '解散式之夜', date: '2023-01-03' },\n        { id: 4, number: '第4话', title: '初阵', date: '2023-01-04' },\n        { id: 5, number: '第5话', title: '心脏的跳动声', date: '2023-01-05' },\n        { id: 6, number: '第6话', title: '少女所见的世界', date: '2023-01-06' },\n        { id: 7, number: '第7话', title: '小小的刀刃', date: '2023-01-07' },\n        { id: 8, number: '第8话', title: '咆哮', date: '2023-01-08' },\n        { id: 9, number: '第9话', title: '左臂的去向', date: '2023-01-09' },\n        { id: 10, number: '第10话', title: '应对', date: '2023-01-10' }\n      ]\n    }\n  },\n  computed: {\n    sortedChapters() {\n      const chapters = [...this.chapters]\n      if (this.sortOrder === 'asc') {\n        return chapters.sort((a, b) => a.id - b.id)\n      } else {\n        return chapters.sort((a, b) => b.id - a.id)\n      }\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    startReading() {\n      // 开始阅读第一章\n      const firstChapter = this.sortOrder === 'asc' ?\n        Math.min(...this.chapters.map(c => c.id)) :\n        Math.max(...this.chapters.map(c => c.id))\n      this.readChapter(firstChapter)\n    },\n    readChapter(chapterId) {\n      this.$router.push({\n        name: 'ComicReader',\n        params: {\n          id: this.id,\n          chapterId: chapterId\n        }\n      })\n    },\n    sortChapters() {\n      // 排序逻辑已在computed中处理\n    }\n  },\n  mounted() {\n    // 根据路由参数加载对应的漫画数据\n    console.log('Comic ID:', this.id)\n  }\n}\n</script>\n\n<style scoped>\n.comic-detail {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  gap: 20px;\n}\n\n.back-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.back-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-info-section {\n  display: flex;\n  gap: 30px;\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.comic-cover-large {\n  flex-shrink: 0;\n}\n\n.comic-cover-large img {\n  width: 300px;\n  height: 400px;\n  object-fit: cover;\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-details {\n  flex: 1;\n}\n\n.comic-title {\n  font-size: 32px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.comic-meta {\n  margin-bottom: 25px;\n}\n\n.comic-meta p {\n  margin-bottom: 10px;\n  font-size: 16px;\n  color: #666;\n}\n\n.comic-description {\n  margin-bottom: 30px;\n}\n\n.comic-description h3 {\n  font-size: 18px;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.comic-description p {\n  line-height: 1.6;\n  color: #666;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n}\n\n.chapters-section {\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.section-header h3 {\n  font-size: 20px;\n  color: #333;\n  margin: 0;\n}\n\n.chapters-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 15px;\n}\n\n.chapter-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border: 1px solid #eee;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.chapter-item:hover {\n  background-color: #f8f9fa;\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.chapter-number {\n  font-weight: bold;\n  color: #667eea;\n  min-width: 80px;\n}\n\n.chapter-title {\n  flex: 1;\n  margin-left: 15px;\n  color: #333;\n}\n\n.chapter-date {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .header {\n    height: auto;\n    min-height: 60px;\n  }\n\n  .header-content {\n    padding: 0 15px;\n    height: 60px;\n  }\n\n  .back-btn {\n    font-size: 14px;\n  }\n\n  .header-content h2 {\n    font-size: 16px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    max-width: 200px;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .comic-info-section {\n    flex-direction: column;\n    padding: 20px;\n    gap: 20px;\n  }\n\n  .comic-cover-large {\n    align-self: center;\n  }\n\n  .comic-cover-large img {\n    width: 180px;\n    height: 250px;\n    margin: 0 auto;\n    display: block;\n  }\n\n  .comic-title {\n    font-size: 22px;\n    text-align: center;\n    margin-bottom: 15px;\n  }\n\n  .comic-meta {\n    text-align: center;\n    margin-bottom: 20px;\n  }\n\n  .comic-meta p {\n    font-size: 15px;\n    margin-bottom: 8px;\n  }\n\n  .comic-description {\n    margin-bottom: 25px;\n  }\n\n  .comic-description h3 {\n    font-size: 16px;\n    text-align: center;\n  }\n\n  .comic-description p {\n    font-size: 14px;\n    line-height: 1.6;\n    text-align: justify;\n  }\n\n  .action-buttons {\n    justify-content: center;\n    gap: 10px;\n  }\n\n  .chapters-section {\n    padding: 20px;\n  }\n\n  .section-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: center;\n    text-align: center;\n  }\n\n  .chapters-grid {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n\n  .chapter-item {\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .chapter-number {\n    min-width: 70px;\n    font-size: 14px;\n  }\n\n  .chapter-title {\n    font-size: 14px;\n    margin-left: 12px;\n  }\n\n  .chapter-date {\n    font-size: 11px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    padding: 0 12px;\n    height: 50px;\n  }\n\n  .header-content h2 {\n    font-size: 14px;\n    max-width: 150px;\n  }\n\n  .back-btn {\n    font-size: 13px;\n  }\n\n  .main-content {\n    padding: 12px;\n  }\n\n  .comic-info-section {\n    padding: 15px;\n    gap: 15px;\n  }\n\n  .comic-cover-large img {\n    width: 150px;\n    height: 210px;\n  }\n\n  .comic-title {\n    font-size: 18px;\n    margin-bottom: 12px;\n  }\n\n  .comic-meta p {\n    font-size: 14px;\n    margin-bottom: 6px;\n  }\n\n  .comic-description {\n    margin-bottom: 20px;\n  }\n\n  .comic-description h3 {\n    font-size: 15px;\n    margin-bottom: 8px;\n  }\n\n  .comic-description p {\n    font-size: 13px;\n    line-height: 1.5;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .action-buttons .el-button {\n    width: 100%;\n    padding: 12px;\n  }\n\n  .chapters-section {\n    padding: 15px;\n  }\n\n  .section-header h3 {\n    font-size: 16px;\n  }\n\n  .chapters-grid {\n    gap: 10px;\n  }\n\n  .chapter-item {\n    padding: 12px;\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .chapter-number {\n    min-width: auto;\n    font-weight: bold;\n    color: #667eea;\n  }\n\n  .chapter-title {\n    margin-left: 0;\n    font-size: 13px;\n    color: #333;\n  }\n\n  .chapter-date {\n    margin-left: 0;\n    font-size: 10px;\n    color: #999;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .header-content {\n    padding: 0 10px;\n  }\n\n  .header-content h2 {\n    font-size: 13px;\n    max-width: 120px;\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n\n  .comic-info-section {\n    padding: 12px;\n  }\n\n  .comic-cover-large img {\n    width: 130px;\n    height: 180px;\n  }\n\n  .comic-title {\n    font-size: 16px;\n  }\n\n  .comic-meta p {\n    font-size: 13px;\n  }\n\n  .comic-description p {\n    font-size: 12px;\n  }\n\n  .chapters-section {\n    padding: 12px;\n  }\n\n  .chapter-item {\n    padding: 10px;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ComicDetail.vue?vue&type=template&id=239f2f50&scoped=true\"\nimport script from \"./ComicDetail.vue?vue&type=script&lang=js\"\nexport * from \"./ComicDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./ComicDetail.vue?vue&type=style&index=0&id=239f2f50&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"239f2f50\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"comic-reader\",class:{ 'fullscreen': _vm.isFullscreen }},[_c('div',{staticClass:\"top-controls\",class:{ 'hidden': _vm.controlsHidden }},[_c('div',{staticClass:\"controls-left\"},[_c('el-button',{staticClass:\"control-btn\",attrs:{\"icon\":\"el-icon-arrow-left\",\"type\":\"text\"},on:{\"click\":_vm.goBack}},[_vm._v(\" 返回 \")]),_c('span',{staticClass:\"chapter-info\"},[_vm._v(_vm._s(_vm.currentChapter.title))])],1),_c('div',{staticClass:\"controls-right\"},[_c('el-button-group',{staticClass:\"reading-mode-toggle\"},[_c('el-button',{attrs:{\"type\":_vm.readingMode === 'horizontal' ? 'primary' : '',\"size\":\"small\"},on:{\"click\":function($event){return _vm.setReadingMode('horizontal')}}},[_vm._v(\" 左右滑动 \")]),_c('el-button',{attrs:{\"type\":_vm.readingMode === 'vertical' ? 'primary' : '',\"size\":\"small\"},on:{\"click\":function($event){return _vm.setReadingMode('vertical')}}},[_vm._v(\" 上下拼接 \")])],1),_c('el-button',{staticClass:\"control-btn\",attrs:{\"icon\":_vm.isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen',\"type\":\"text\"},on:{\"click\":_vm.toggleFullscreen}},[_vm._v(\" \"+_vm._s(_vm.isFullscreen ? '退出全屏' : '全屏')+\" \")])],1)]),_c('div',{staticClass:\"reading-area\",on:{\"click\":_vm.toggleControls,\"touchstart\":_vm.handleTouchStart,\"touchend\":_vm.handleTouchEnd,\"touchmove\":_vm.handleTouchMove}},[(_vm.readingMode === 'horizontal')?_c('div',{staticClass:\"horizontal-reader\"},[_c('div',{ref:\"imageContainer\",staticClass:\"image-container\"},_vm._l((_vm.currentChapter.images),function(image,index){return _c('img',{key:index,staticClass:\"comic-image\",class:{ 'active': index === _vm.currentPage },attrs:{\"src\":image,\"alt\":`第${_vm.currentPage + 1}页`},on:{\"load\":_vm.onImageLoad,\"error\":_vm.onImageError}})}),0),_c('div',{staticClass:\"nav-buttons\"},[(_vm.currentPage > 0)?_c('el-button',{staticClass:\"nav-btn nav-btn-left\",attrs:{\"icon\":\"el-icon-arrow-left\",\"circle\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.prevPage.apply(null, arguments)}}}):_vm._e(),(_vm.currentPage < _vm.currentChapter.images.length - 1)?_c('el-button',{staticClass:\"nav-btn nav-btn-right\",attrs:{\"icon\":\"el-icon-arrow-right\",\"circle\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.nextPage.apply(null, arguments)}}}):_vm._e()],1)]):_c('div',{ref:\"verticalReader\",staticClass:\"vertical-reader\"},_vm._l((_vm.currentChapter.images),function(image,index){return _c('img',{key:index,staticClass:\"comic-image-vertical\",attrs:{\"src\":image,\"alt\":`第${index + 1}页`},on:{\"load\":_vm.onImageLoad,\"error\":_vm.onImageError}})}),0)]),_c('div',{staticClass:\"bottom-controls\",class:{ 'hidden': _vm.controlsHidden }},[(_vm.readingMode === 'horizontal')?_c('div',{staticClass:\"page-info\"},[_c('span',[_vm._v(_vm._s(_vm.currentPage + 1)+\" / \"+_vm._s(_vm.currentChapter.images.length))])]):_vm._e(),_c('div',{staticClass:\"chapter-navigation\"},[_c('el-button',{attrs:{\"disabled\":!_vm.hasPrevChapter,\"size\":\"small\"},on:{\"click\":_vm.prevChapter}},[_vm._v(\" 上一章 \")]),_c('el-select',{staticStyle:{\"width\":\"200px\"},attrs:{\"size\":\"small\"},on:{\"change\":_vm.changeChapter},model:{value:(_vm.currentChapterId),callback:function ($$v) {_vm.currentChapterId=$$v},expression:\"currentChapterId\"}},_vm._l((_vm.allChapters),function(chapter){return _c('el-option',{key:chapter.id,attrs:{\"label\":chapter.title,\"value\":chapter.id}})}),1),_c('el-button',{attrs:{\"disabled\":!_vm.hasNextChapter,\"size\":\"small\"},on:{\"click\":_vm.nextChapter}},[_vm._v(\" 下一章 \")])],1),_c('div',{staticClass:\"quick-actions\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":_vm.goHome}},[_vm._v(\"回到首页\")])],1)]),(_vm.loading)?_c('div',{staticClass:\"loading-overlay\"},[_c('el-loading-text',[_vm._v(\"加载中...\")])],1):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div\n      class=\"reading-area\"\n      @click=\"toggleControls\"\n      @touchstart=\"handleTouchStart\"\n      @touchend=\"handleTouchEnd\"\n      @touchmove=\"handleTouchMove\"\n    >\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      touchStartX: 0,\n      touchStartY: 0,\n      touchStartTime: 0,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    },\n    handleTouchStart(event) {\n      if (event.touches.length === 1) {\n        this.touchStartX = event.touches[0].clientX\n        this.touchStartY = event.touches[0].clientY\n        this.touchStartTime = Date.now()\n      }\n    },\n    handleTouchMove(event) {\n      // 防止页面滚动（仅在水平滑动模式下）\n      if (this.readingMode === 'horizontal' && event.touches.length === 1) {\n        const touchX = event.touches[0].clientX\n        const touchY = event.touches[0].clientY\n        const deltaX = Math.abs(touchX - this.touchStartX)\n        const deltaY = Math.abs(touchY - this.touchStartY)\n\n        // 如果水平滑动距离大于垂直滑动距离，阻止默认滚动\n        if (deltaX > deltaY) {\n          event.preventDefault()\n        }\n      }\n    },\n    handleTouchEnd(event) {\n      if (!this.touchStartX || event.changedTouches.length !== 1) return\n\n      const touchEndX = event.changedTouches[0].clientX\n      const touchEndY = event.changedTouches[0].clientY\n      const touchEndTime = Date.now()\n\n      const deltaX = this.touchStartX - touchEndX\n      const deltaY = this.touchStartY - touchEndY\n      const deltaTime = touchEndTime - this.touchStartTime\n\n      // 检查是否为有效的滑动手势\n      const minSwipeDistance = 50 // 最小滑动距离\n      const maxSwipeTime = 500 // 最大滑动时间\n      const maxVerticalDistance = 100 // 最大垂直偏移\n\n      if (Math.abs(deltaX) > minSwipeDistance &&\n          Math.abs(deltaY) < maxVerticalDistance &&\n          deltaTime < maxSwipeTime) {\n\n        if (this.readingMode === 'horizontal') {\n          // 水平滑动模式下的手势处理\n          if (deltaX > 0) {\n            // 向左滑动，下一页\n            this.nextPage()\n          } else {\n            // 向右滑动，上一页\n            this.prevPage()\n          }\n        }\n      } else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {\n        // 点击手势，切换控制栏显示\n        this.toggleControls()\n      }\n\n      // 重置触摸状态\n      this.touchStartX = 0\n      this.touchStartY = 0\n      this.touchStartTime = 0\n    },\n    handleKeydown(event) {\n      if (this.readingMode === 'horizontal') {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault()\n            this.prevPage()\n            break\n          case 'ArrowRight':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case ' ':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case 'Escape':\n            if (this.isFullscreen) {\n              this.exitFullscreen()\n            }\n            break\n        }\n      }\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 添加键盘事件监听\n    document.addEventListener('keydown', this.handleKeydown)\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 监听webkit全屏状态变化（Safari）\n    document.addEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    document.removeEventListener('keydown', this.handleKeydown)\n    document.removeEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n    document.removeEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .top-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .bottom-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .controls-left {\n    gap: 8px;\n    flex: 1;\n    min-width: 0;\n  }\n\n  .controls-right {\n    gap: 8px;\n    flex-shrink: 0;\n  }\n\n  .control-btn {\n    font-size: 14px;\n    padding: 5px 8px;\n  }\n\n  .chapter-info {\n    font-size: 13px;\n    max-width: 120px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .chapter-navigation {\n    gap: 8px;\n    flex: 1;\n    justify-content: center;\n  }\n\n  .chapter-navigation .el-select {\n    width: 140px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .page-info {\n    font-size: 13px;\n    min-width: 60px;\n  }\n\n  .quick-actions .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .nav-btn {\n    width: 45px;\n    height: 45px;\n    font-size: 16px;\n  }\n\n  .nav-buttons {\n    padding: 0 15px;\n  }\n\n  .vertical-reader {\n    padding: 10px 5px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 3px;\n    max-width: calc(100% - 10px);\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n    padding: 0 10px;\n    flex-wrap: wrap;\n  }\n\n  .controls-left {\n    gap: 6px;\n    order: 1;\n    width: 100%;\n    justify-content: space-between;\n    margin-bottom: 5px;\n  }\n\n  .controls-right {\n    gap: 6px;\n    order: 2;\n    width: 100%;\n    justify-content: center;\n  }\n\n  .control-btn {\n    font-size: 12px;\n    padding: 4px 6px;\n  }\n\n  .chapter-info {\n    font-size: 12px;\n    max-width: 100px;\n    flex: 1;\n  }\n\n  .reading-mode-toggle {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    flex: 1;\n  }\n\n  .bottom-controls {\n    height: auto;\n    min-height: 50px;\n    flex-direction: column;\n    gap: 8px;\n    padding: 8px 10px;\n  }\n\n  .page-info {\n    order: 1;\n    text-align: center;\n    font-size: 12px;\n    min-width: auto;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    gap: 6px;\n    width: 100%;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .chapter-navigation .el-select {\n    width: 120px !important;\n    margin: 0 5px;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n    min-width: 60px;\n  }\n\n  .quick-actions {\n    order: 3;\n    text-align: center;\n    min-width: auto;\n  }\n\n  .quick-actions .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n    font-size: 14px;\n  }\n\n  .nav-buttons {\n    padding: 0 10px;\n  }\n\n  .vertical-reader {\n    padding: 5px 3px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n    max-width: calc(100% - 6px);\n  }\n\n  .loading-overlay {\n    font-size: 16px;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .top-controls {\n    height: 45px;\n    padding: 0 8px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 4px;\n  }\n\n  .control-btn {\n    font-size: 11px;\n    padding: 3px 5px;\n  }\n\n  .chapter-info {\n    font-size: 11px;\n    max-width: 80px;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 2px 4px;\n    font-size: 9px;\n  }\n\n  .bottom-controls {\n    padding: 6px 8px;\n    gap: 6px;\n  }\n\n  .page-info {\n    font-size: 11px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 100px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    min-width: 50px;\n  }\n\n  .quick-actions .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n  }\n\n  .nav-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 12px;\n  }\n\n  .nav-buttons {\n    padding: 0 8px;\n  }\n\n  .vertical-reader {\n    padding: 3px 2px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 1px;\n    max-width: calc(100% - 4px);\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.7;\n    background-color: rgba(0, 0, 0, 0.8) !important;\n  }\n\n  .nav-btn:active {\n    opacity: 1;\n    transform: scale(0.95);\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n\n  /* 增加触摸区域 */\n  .control-btn,\n  .reading-mode-toggle .el-button,\n  .chapter-navigation .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n  }\n\n  /* 防止双击缩放 */\n  .reading-area {\n    touch-action: pan-y pinch-zoom;\n  }\n\n  .horizontal-reader {\n    touch-action: pan-y;\n  }\n\n  /* 优化滚动性能 */\n  .vertical-reader {\n    -webkit-overflow-scrolling: touch;\n    scroll-behavior: smooth;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  .top-controls,\n  .bottom-controls {\n    height: 45px;\n  }\n\n  .top-controls {\n    padding: 0 15px;\n  }\n\n  .bottom-controls {\n    padding: 0 15px;\n    flex-direction: row;\n    justify-content: space-between;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    flex: 1;\n    max-width: 300px;\n  }\n\n  .page-info {\n    order: 1;\n  }\n\n  .quick-actions {\n    order: 3;\n  }\n\n  .vertical-reader {\n    padding: 5px 10px;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicReader.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicReader.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ComicReader.vue?vue&type=template&id=2a02587f&scoped=true\"\nimport script from \"./ComicReader.vue?vue&type=script&lang=js\"\nexport * from \"./ComicReader.vue?vue&type=script&lang=js\"\nimport style0 from \"./ComicReader.vue?vue&type=style&index=0&id=2a02587f&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2a02587f\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport ComicList from '../views/ComicList.vue'\nimport ComicDetail from '../views/ComicDetail.vue'\nimport ComicReader from '../views/ComicReader.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'ComicList',\n    component: ComicList\n  },\n  {\n    path: '/comic/:id',\n    name: 'ComicDetail',\n    component: ComicDetail,\n    props: true\n  },\n  {\n    path: '/comic/:id/chapter/:chapterId',\n    name: 'ComicReader',\n    component: ComicReader,\n    props: true\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\n\nVue.config.productionTip = false\n\nVue.use(ElementUI)\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicDetail.vue?vue&type=style&index=0&id=239f2f50&prod&scoped=true&lang=css\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicReader.vue?vue&type=style&index=0&id=2a02587f&prod&scoped=true&lang=css\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ComicList.vue?vue&type=style&index=0&id=686e526f&prod&scoped=true&lang=css\""], "sourceRoot": ""}