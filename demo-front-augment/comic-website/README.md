# 漫画网站 - 在线漫画阅读平台

一个基于 Vue2 + Element UI 开发的现代化漫画阅读网站，支持PC端和移动端访问，提供优质的漫画阅读体验。

## 🌟 项目特色

### 核心功能
- 📚 **漫画列表浏览** - 响应式网格布局，支持搜索和分页
- 📖 **漫画详情展示** - 详细信息展示，章节列表管理
- 🎯 **双模式阅读** - 支持左右滑动和上下拼接两种阅读模式
- 📱 **响应式设计** - 完美适配PC端和移动端
- 🎨 **现代化UI** - 基于Element UI的精美界面设计

### 阅读体验
- 🔄 **实时模式切换** - 一键切换阅读模式，满足不同阅读习惯
- 🖱️ **多种操作方式** - 支持鼠标点击、键盘快捷键、触摸手势
- 🌙 **沉浸式阅读** - 全屏模式，智能控制栏隐藏
- 💾 **偏好记忆** - 自动保存用户阅读偏好设置
- ⚡ **流畅体验** - 优化的图片加载和页面切换动画

## 🚀 快速开始

### 环境要求
- Node.js >= 12.0.0
- npm >= 6.0.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd comic-website
   ```

2. **安装依赖**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **启动开发服务器**
   ```bash
   npm run serve
   ```

4. **访问网站**
   ```
   打开浏览器访问: http://localhost:8080
   ```

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
comic-website/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/                   # 源代码
│   ├── views/            # 页面组件
│   │   ├── ComicList.vue    # 漫画列表页
│   │   ├── ComicDetail.vue  # 漫画详情页
│   │   └── ComicReader.vue  # 漫画阅读页
│   ├── router/           # 路由配置
│   │   └── index.js        # 路由定义
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── package.json          # 项目配置
├── babel.config.js       # Babel配置
└── README.md            # 项目文档
```

## 🎮 功能详解

### 1. 漫画列表页面 (ComicList.vue)

**主要功能：**
- 响应式网格布局展示漫画卡片
- 实时搜索功能
- 分页控件（支持每页数量调整）
- 悬停效果和动画

**技术特点：**
- 使用CSS Grid实现响应式布局
- 计算属性实现实时搜索过滤
- Element UI分页组件集成

### 2. 漫画详情页面 (ComicDetail.vue)

**主要功能：**
- 漫画封面和详细信息展示
- 章节列表网格布局
- 正序/倒序排列切换
- 快速开始阅读

**技术特点：**
- Flexbox布局实现信息展示
- 计算属性实现章节排序
- 路由参数传递

### 3. 漫画阅读页面 (ComicReader.vue) ⭐

**核心亮点 - 双阅读模式：**

#### 模式1：左右滑动模式
- 单页显示，类似翻书体验
- 支持左右箭头按钮导航
- 键盘快捷键支持（←→空格键）
- 移动端触摸手势支持
- 显示当前页码/总页数

#### 模式2：上下拼接模式（默认）
- 垂直连续显示所有页面
- 类似长图阅读体验
- 支持滚动浏览
- 适合移动端阅读

**其他功能：**
- 🔄 一键切换阅读模式
- 🖥️ 全屏阅读支持
- 🎯 智能控制栏（点击隐藏/显示）
- 📖 章节快速跳转
- 💾 用户偏好记忆（localStorage）
- ⚡ 图片预加载和错误处理

## 🎨 响应式设计

### PC端优化
- 大屏幕网格布局（4-5列）
- 鼠标悬停效果
- 键盘快捷键支持
- 大尺寸控制按钮

### 移动端优化
- 自适应网格布局（2列）
- 触摸友好的界面
- 手势操作支持
- 移动端专用控制栏
- 优化的字体大小和间距

### 断点设计
- `768px` - 平板端适配
- `480px` - 手机端适配
- 使用CSS媒体查询实现

## 🛠 技术栈

### 前端框架
- **Vue 2.6.14** - 渐进式JavaScript框架
- **Vue Router 3.5.1** - 官方路由管理器
- **Element UI 2.15.14** - 基于Vue的组件库

### 开发工具
- **Vue CLI 4.5** - Vue项目脚手架
- **Babel** - JavaScript编译器
- **ESLint** - 代码质量检查

### 样式技术
- **CSS3** - 现代CSS特性
- **Flexbox & Grid** - 现代布局技术
- **媒体查询** - 响应式设计
- **CSS动画** - 流畅的交互效果

## 🎯 使用指南

### 基本操作

1. **浏览漫画**
   - 在首页浏览漫画列表
   - 使用搜索框查找特定漫画
   - 点击漫画卡片查看详情

2. **阅读漫画**
   - 在详情页点击"开始阅读"
   - 选择喜欢的阅读模式
   - 使用导航控件切换章节

### 阅读模式切换

**左右滑动模式：**
- 适合逐页细读
- 支持键盘方向键
- 移动端支持左右滑动手势

**上下拼接模式：**
- 适合快速浏览
- 连续阅读体验
- 支持滚动操作

### 快捷键

| 按键 | 功能 |
|------|------|
| ← | 上一页（左右滑动模式） |
| → | 下一页（左右滑动模式） |
| 空格 | 下一页（左右滑动模式） |
| F11 | 全屏切换 |
| ESC | 退出全屏 |

## 📱 移动端体验

### 触摸手势
- **左滑** - 下一页
- **右滑** - 上一页
- **点击** - 显示/隐藏控制栏
- **双指缩放** - 图片缩放（计划中）

### 移动端优化
- 触摸友好的按钮大小
- 优化的控制栏布局
- 自适应图片大小
- 流畅的滑动体验

## 🔧 自定义配置

### 修改默认设置

在 `ComicReader.vue` 中可以修改：

```javascript
// 默认阅读模式
readingMode: 'vertical', // 'horizontal' | 'vertical'

// 自动隐藏控制栏时间
autoHideDelay: 3000, // 毫秒

// 图片加载超时时间
imageTimeout: 10000, // 毫秒
```

### 添加新漫画

在 `ComicList.vue` 的 `comics` 数组中添加：

```javascript
{
  id: 13,
  title: '新漫画标题',
  cover: '封面图片URL',
  latestChapter: '最新章节',
  updateTime: '更新时间'
}
```

## 🌐 在线图片说明

项目使用 Unsplash API 提供的高质量在线图片：

- **封面图片**: 使用不同色调的同一张图片，确保视觉一致性
- **阅读页面**: 动态生成不同色调的图片，模拟真实漫画页面
- **响应式**: 根据设备尺寸自动调整图片大小
- **加载优化**: 支持图片懒加载和错误处理

### 图片URL格式
```
https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240
```

参数说明：
- `w` - 宽度
- `h` - 高度  
- `fit=crop` - 裁剪模式
- `sat=-100` - 饱和度
- `hue=240` - 色调

## 🚀 部署指南

### 开发环境部署
```bash
npm run serve
```

### 生产环境构建
```bash
npm run build
```

### 静态文件部署
构建完成后，将 `dist` 目录部署到任何静态文件服务器：
- Nginx
- Apache
- GitHub Pages
- Netlify
- Vercel

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue 官方风格指南
- 添加适当的注释
- 保持代码简洁易读

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 优秀的前端框架
- [Element UI](https://element.eleme.io/) - 精美的组件库
- [Unsplash](https://unsplash.com/) - 高质量图片服务

---

**享受阅读！** 📚✨
