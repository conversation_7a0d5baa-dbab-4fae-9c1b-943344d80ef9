{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748420855536}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicReader.vue"], "names": [], "mappings": ";AAiIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ComicReader.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div class=\"reading-area\" @click=\"toggleControls\">\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .top-controls,\n  .bottom-controls {\n    padding: 0 10px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 10px;\n  }\n\n  .chapter-info {\n    font-size: 14px;\n    max-width: 150px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .chapter-navigation {\n    gap: 10px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 150px !important;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n  }\n\n  .vertical-reader {\n    padding: 10px 0;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n  }\n\n  .bottom-controls {\n    height: 50px;\n    flex-direction: column;\n    gap: 5px;\n    padding: 5px 10px;\n  }\n\n  .chapter-navigation {\n    order: 1;\n  }\n\n  .page-info,\n  .quick-actions {\n    order: 2;\n    min-width: auto;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 5px 8px;\n    font-size: 12px;\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.5;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n}\n</style>\n"]}]}