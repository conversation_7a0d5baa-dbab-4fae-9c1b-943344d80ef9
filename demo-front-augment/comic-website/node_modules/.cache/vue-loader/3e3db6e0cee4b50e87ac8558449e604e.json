{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue", "mtime": 1748421697518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwJwp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAOA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml {\n  /* 防止iOS Safari双击缩放 */\n  touch-action: manipulation;\n  /* 优化移动端滚动 */\n  -webkit-overflow-scrolling: touch;\n}\n\nbody {\n  background-color: #f5f5f5;\n  /* 防止移动端橡皮筋效果 */\n  overscroll-behavior: none;\n  /* 优化字体渲染 */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  /* 防止移动端文本选择 */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n#app {\n  font-family: 'Avenir', Helvetica, Arial, sans-serif, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n}\n\n/* 移动端输入框优化 */\ninput, textarea, select {\n  -webkit-user-select: text;\n  -moz-user-select: text;\n  -ms-user-select: text;\n  user-select: text;\n}\n\n/* Element UI 移动端优化 */\n.el-button {\n  /* 增加触摸区域 */\n  min-height: 36px;\n  /* 防止双击缩放 */\n  touch-action: manipulation;\n}\n\n.el-input__inner {\n  /* 防止iOS输入框缩放 */\n  font-size: 16px;\n  /* 优化移动端输入体验 */\n  -webkit-appearance: none;\n  appearance: none;\n  border-radius: 4px;\n}\n\n.el-select .el-input__inner {\n  cursor: pointer;\n}\n\n/* 分页组件移动端优化 */\n.el-pagination {\n  text-align: center;\n}\n\n.el-pagination .el-pager li {\n  min-width: 32px;\n  height: 32px;\n  line-height: 32px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .el-container {\n    flex-direction: column;\n  }\n\n  /* Element UI 组件移动端调整 */\n  .el-button {\n    min-height: 40px;\n    padding: 8px 15px;\n  }\n\n  .el-button--small {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-select .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n\n  /* 优化弹窗在移动端的显示 */\n  .el-dialog {\n    width: 90% !important;\n    margin: 5vh auto !important;\n  }\n\n  .el-message-box {\n    width: 90% !important;\n  }\n}\n\n@media (max-width: 480px) {\n  /* 超小屏幕优化 */\n  .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n    padding: 10px 15px;\n    font-size: 14px;\n  }\n\n  .el-button--small {\n    min-height: 40px;\n    padding: 8px 12px;\n    font-size: 13px;\n  }\n\n  .el-input__inner {\n    height: 44px;\n    line-height: 44px;\n    font-size: 16px; /* 防止iOS缩放 */\n  }\n\n  .el-pagination .el-pager li {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n    margin: 0 2px;\n  }\n\n  .el-pagination .btn-prev,\n  .el-pagination .btn-next {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  /* 横屏时减少垂直空间占用 */\n  .el-button {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 高分辨率屏幕优化 */\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n  /* 优化高分辨率屏幕的显示效果 */\n  #app {\n    -webkit-font-smoothing: subpixel-antialiased;\n  }\n}\n\n/* 暗色模式支持（预留） */\n@media (prefers-color-scheme: dark) {\n  /* 可以在这里添加暗色模式样式 */\n}\n\n/* 减少动画的用户偏好 */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n</style>\n"]}]}