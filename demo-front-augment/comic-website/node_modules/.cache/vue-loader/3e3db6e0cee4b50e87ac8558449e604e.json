{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue", "mtime": 1748420160092}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwJwp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAOA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: 'Avenir', Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n}\n\nbody {\n  background-color: #f5f5f5;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .el-container {\n    flex-direction: column;\n  }\n}\n</style>\n"]}]}