{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue?vue&type=style&index=0&id=40ddb842&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue", "mtime": 1748420259114}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicDetail.vue"], "names": [], "mappings": ";AAoJA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ComicDetail.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-detail\">\n    <!-- 顶部导航 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"back-btn\">\n          返回列表\n        </el-button>\n        <h2>{{ comic.title }}</h2>\n      </div>\n    </el-header>\n\n    <!-- 主要内容 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画信息区 -->\n        <div class=\"comic-info-section\">\n          <div class=\"comic-cover-large\">\n            <img :src=\"comic.cover\" :alt=\"comic.title\" />\n          </div>\n          <div class=\"comic-details\">\n            <h1 class=\"comic-title\">{{ comic.title }}</h1>\n            <div class=\"comic-meta\">\n              <p><strong>作者:</strong> {{ comic.author }}</p>\n              <p><strong>状态:</strong> \n                <el-tag :type=\"comic.status === '连载中' ? 'success' : 'info'\">\n                  {{ comic.status }}\n                </el-tag>\n              </p>\n              <p><strong>最新章节:</strong> {{ comic.latestChapter }}</p>\n              <p><strong>更新时间:</strong> {{ comic.updateTime }}</p>\n            </div>\n            <div class=\"comic-description\">\n              <h3>简介</h3>\n              <p>{{ comic.description }}</p>\n            </div>\n            <div class=\"action-buttons\">\n              <el-button type=\"primary\" size=\"large\" @click=\"startReading\">\n                开始阅读\n              </el-button>\n              <el-button size=\"large\">收藏</el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 章节列表区 -->\n        <div class=\"chapters-section\">\n          <div class=\"section-header\">\n            <h3>章节列表</h3>\n            <div class=\"sort-controls\">\n              <el-radio-group v-model=\"sortOrder\" @change=\"sortChapters\">\n                <el-radio-button label=\"asc\">正序</el-radio-button>\n                <el-radio-button label=\"desc\">倒序</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n          \n          <div class=\"chapters-grid\">\n            <div \n              v-for=\"chapter in sortedChapters\" \n              :key=\"chapter.id\"\n              class=\"chapter-item\"\n              @click=\"readChapter(chapter.id)\"\n            >\n              <div class=\"chapter-number\">{{ chapter.number }}</div>\n              <div class=\"chapter-title\">{{ chapter.title }}</div>\n              <div class=\"chapter-date\">{{ chapter.date }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicDetail',\n  props: ['id'],\n  data() {\n    return {\n      sortOrder: 'desc',\n      comic: {\n        id: 1,\n        title: '进击的巨人',\n        author: '諫山創',\n        status: '已完结',\n        cover: 'https://via.placeholder.com/300x400/4CAF50/white?text=进击的巨人',\n        latestChapter: '第139话',\n        updateTime: '2024-01-15',\n        description: '《进击的巨人》是日本漫画家諫山創创作的漫画作品。故事讲述了人类与巨人之间的战斗，以及主人公艾伦·耶格尔等人为了人类的自由而奋斗的故事。作品以其独特的世界观、复杂的剧情和深刻的主题而广受好评。'\n      },\n      chapters: [\n        { id: 1, number: '第1话', title: '致两千年后的你', date: '2023-01-01' },\n        { id: 2, number: '第2话', title: '那一天', date: '2023-01-02' },\n        { id: 3, number: '第3话', title: '解散式之夜', date: '2023-01-03' },\n        { id: 4, number: '第4话', title: '初阵', date: '2023-01-04' },\n        { id: 5, number: '第5话', title: '心脏的跳动声', date: '2023-01-05' },\n        { id: 6, number: '第6话', title: '少女所见的世界', date: '2023-01-06' },\n        { id: 7, number: '第7话', title: '小小的刀刃', date: '2023-01-07' },\n        { id: 8, number: '第8话', title: '咆哮', date: '2023-01-08' },\n        { id: 9, number: '第9话', title: '左臂的去向', date: '2023-01-09' },\n        { id: 10, number: '第10话', title: '应对', date: '2023-01-10' }\n      ]\n    }\n  },\n  computed: {\n    sortedChapters() {\n      const chapters = [...this.chapters]\n      if (this.sortOrder === 'asc') {\n        return chapters.sort((a, b) => a.id - b.id)\n      } else {\n        return chapters.sort((a, b) => b.id - a.id)\n      }\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    startReading() {\n      // 开始阅读第一章\n      const firstChapter = this.sortOrder === 'asc' ? \n        Math.min(...this.chapters.map(c => c.id)) : \n        Math.max(...this.chapters.map(c => c.id))\n      this.readChapter(firstChapter)\n    },\n    readChapter(chapterId) {\n      this.$router.push({ \n        name: 'ComicReader', \n        params: { \n          id: this.id, \n          chapterId: chapterId \n        } \n      })\n    },\n    sortChapters() {\n      // 排序逻辑已在computed中处理\n    }\n  },\n  mounted() {\n    // 根据路由参数加载对应的漫画数据\n    console.log('Comic ID:', this.id)\n  }\n}\n</script>\n\n<style scoped>\n.comic-detail {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  gap: 20px;\n}\n\n.back-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.back-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-info-section {\n  display: flex;\n  gap: 30px;\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.comic-cover-large {\n  flex-shrink: 0;\n}\n\n.comic-cover-large img {\n  width: 300px;\n  height: 400px;\n  object-fit: cover;\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-details {\n  flex: 1;\n}\n\n.comic-title {\n  font-size: 32px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.comic-meta {\n  margin-bottom: 25px;\n}\n\n.comic-meta p {\n  margin-bottom: 10px;\n  font-size: 16px;\n  color: #666;\n}\n\n.comic-description {\n  margin-bottom: 30px;\n}\n\n.comic-description h3 {\n  font-size: 18px;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.comic-description p {\n  line-height: 1.6;\n  color: #666;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n}\n\n.chapters-section {\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.section-header h3 {\n  font-size: 20px;\n  color: #333;\n  margin: 0;\n}\n\n.chapters-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 15px;\n}\n\n.chapter-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border: 1px solid #eee;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.chapter-item:hover {\n  background-color: #f8f9fa;\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.chapter-number {\n  font-weight: bold;\n  color: #667eea;\n  min-width: 80px;\n}\n\n.chapter-title {\n  flex: 1;\n  margin-left: 15px;\n  color: #333;\n}\n\n.chapter-date {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .comic-info-section {\n    flex-direction: column;\n    padding: 20px;\n  }\n  \n  .comic-cover-large img {\n    width: 200px;\n    height: 280px;\n    margin: 0 auto;\n    display: block;\n  }\n  \n  .comic-title {\n    font-size: 24px;\n    text-align: center;\n  }\n  \n  .chapters-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .chapter-item {\n    padding: 12px;\n  }\n  \n  .section-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: flex-start;\n  }\n}\n\n@media (max-width: 480px) {\n  .main-content {\n    padding: 10px;\n  }\n  \n  .comic-info-section,\n  .chapters-section {\n    padding: 15px;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n  }\n}\n</style>\n"]}]}