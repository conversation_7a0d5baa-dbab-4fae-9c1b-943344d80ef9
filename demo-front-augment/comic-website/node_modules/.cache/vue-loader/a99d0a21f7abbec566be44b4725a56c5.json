{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=template&id=211f044f&scoped=true", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748422167106}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1748420070797}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}