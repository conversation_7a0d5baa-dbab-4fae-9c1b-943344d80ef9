{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=style&index=0&id=211f044f&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748420781598}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicList.vue"], "names": [], "mappings": ";AAqMA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ComicList.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div\n            v-for=\"comic in filteredComics\"\n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      totalComics: 0,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        },\n        {\n          id: 7,\n          title: '我的英雄学院',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',\n          latestChapter: '第390话',\n          updateTime: '2024-01-09'\n        },\n        {\n          id: 8,\n          title: '东京喰种',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',\n          latestChapter: '第179话',\n          updateTime: '2024-01-08'\n        },\n        {\n          id: 9,\n          title: '约定的梦幻岛',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',\n          latestChapter: '第181话',\n          updateTime: '2024-01-07'\n        },\n        {\n          id: 10,\n          title: '咒术回战',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',\n          latestChapter: '第245话',\n          updateTime: '2024-01-06'\n        },\n        {\n          id: 11,\n          title: '链锯人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',\n          latestChapter: '第150话',\n          updateTime: '2024-01-05'\n        },\n        {\n          id: 12,\n          title: '间谍过家家',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',\n          latestChapter: '第95话',\n          updateTime: '2024-01-04'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      this.totalComics = filtered.length\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n}\n\n.search-box .el-input {\n  flex: 1;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 10px;\n    gap: 10px;\n  }\n\n  .search-box {\n    margin: 0;\n    max-width: 100%;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 15px;\n  }\n\n  .comic-cover {\n    height: 200px;\n  }\n\n  .comic-info {\n    padding: 10px;\n  }\n\n  .comic-title {\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 480px) {\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n}\n</style>\n"]}]}