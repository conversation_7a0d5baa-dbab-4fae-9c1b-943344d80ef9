{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=style&index=0&id=211f044f&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748422088096}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicList.vue"], "names": [], "mappings": ";AA4MA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ComicList.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div\n            v-for=\"comic in filteredComics\"\n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalFilteredComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        },\n        {\n          id: 7,\n          title: '我的英雄学院',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',\n          latestChapter: '第390话',\n          updateTime: '2024-01-09'\n        },\n        {\n          id: 8,\n          title: '东京喰种',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',\n          latestChapter: '第179话',\n          updateTime: '2024-01-08'\n        },\n        {\n          id: 9,\n          title: '约定的梦幻岛',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',\n          latestChapter: '第181话',\n          updateTime: '2024-01-07'\n        },\n        {\n          id: 10,\n          title: '咒术回战',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',\n          latestChapter: '第245话',\n          updateTime: '2024-01-06'\n        },\n        {\n          id: 11,\n          title: '链锯人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',\n          latestChapter: '第150话',\n          updateTime: '2024-01-05'\n        },\n        {\n          id: 12,\n          title: '间谍过家家',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',\n          latestChapter: '第95话',\n          updateTime: '2024-01-04'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    },\n    totalFilteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      return filtered.length\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 10;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n  position: relative;\n  z-index: 10;\n}\n\n.search-box .el-input {\n  flex: 1;\n  position: relative;\n  z-index: 11;\n}\n\n.search-box .el-button {\n  position: relative;\n  z-index: 12;\n  flex-shrink: 0;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .header {\n    height: auto;\n    min-height: 70px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 15px;\n    gap: 15px;\n  }\n\n  .logo h2 {\n    font-size: 20px;\n    text-align: center;\n  }\n\n  .search-box {\n    margin: 0;\n    max-width: 100%;\n    width: 100%;\n    position: relative;\n    z-index: 20;\n    background: transparent;\n  }\n\n  .search-box .el-input {\n    font-size: 16px; /* 防止iOS缩放 */\n    position: relative;\n    z-index: 21;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: rgba(255, 255, 255, 0.95);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: #333;\n  }\n\n  .search-box .el-input .el-input__inner::placeholder {\n    color: #999;\n  }\n\n  .search-box .el-button {\n    position: relative;\n    z-index: 22;\n    background-color: rgba(255, 255, 255, 0.2);\n    border-color: rgba(255, 255, 255, 0.3);\n    color: white;\n  }\n\n  .search-box .el-button:hover {\n    background-color: rgba(255, 255, 255, 0.3);\n    border-color: rgba(255, 255, 255, 0.5);\n  }\n\n  .user-actions {\n    justify-content: center;\n    width: 100%;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n    gap: 12px;\n  }\n\n  .comic-cover {\n    height: 200px;\n  }\n\n  .comic-info {\n    padding: 12px;\n  }\n\n  .comic-title {\n    font-size: 14px;\n    line-height: 1.3;\n  }\n\n  .comic-latest {\n    font-size: 13px;\n  }\n\n  .comic-update {\n    font-size: 11px;\n  }\n\n  .pagination-wrapper {\n    margin-top: 30px;\n  }\n\n  .pagination-wrapper .el-pagination {\n    text-align: center;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager li {\n    min-width: 32px;\n    height: 32px;\n    line-height: 32px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    padding: 12px;\n    gap: 12px;\n    position: relative;\n    z-index: 100;\n  }\n\n  .logo h2 {\n    font-size: 18px;\n  }\n\n  .search-box {\n    order: 2;\n    width: 100%;\n    margin: 0;\n    position: relative;\n    z-index: 101;\n  }\n\n  .search-box .el-input {\n    position: relative;\n    z-index: 102;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: white;\n    border: 1px solid #dcdfe6;\n    color: #333;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .search-box .el-button {\n    position: relative;\n    z-index: 103;\n    background-color: #409eff;\n    border-color: #409eff;\n    color: white;\n    height: 40px;\n    min-width: 60px;\n  }\n\n  .user-actions {\n    order: 3;\n    width: 100%;\n    justify-content: center;\n    position: relative;\n    z-index: 100;\n  }\n\n  .main-content {\n    padding: 12px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 10px;\n  }\n\n  .comic-cover {\n    height: 180px;\n  }\n\n  .comic-info {\n    padding: 10px;\n  }\n\n  .comic-title {\n    font-size: 13px;\n    margin-bottom: 6px;\n  }\n\n  .comic-latest {\n    font-size: 12px;\n    margin-bottom: 3px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n\n  .user-actions .el-button {\n    padding: 8px 15px;\n    font-size: 14px;\n  }\n\n  .search-box .el-button {\n    padding: 10px 15px;\n  }\n\n  /* 分页组件移动端优化 */\n  .pagination-wrapper .el-pagination {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__sizes {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__total {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .btn-prev,\n  .pagination-wrapper .el-pagination .btn-next {\n    margin: 0 2px 10px 2px;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__jump {\n    margin: 0 5px 10px 0;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .header-content {\n    padding: 10px;\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n\n  .comic-grid {\n    gap: 8px;\n  }\n\n  .comic-cover {\n    height: 160px;\n  }\n\n  .comic-info {\n    padding: 8px;\n  }\n\n  .comic-title {\n    font-size: 12px;\n  }\n\n  .comic-latest {\n    font-size: 11px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n}\n</style>\n"]}]}