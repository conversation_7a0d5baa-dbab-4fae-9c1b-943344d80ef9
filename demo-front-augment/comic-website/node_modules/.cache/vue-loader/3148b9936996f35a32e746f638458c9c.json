{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue", "mtime": 1748421509123}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicDetail.vue"], "names": [], "mappings": ";AA4EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ComicDetail.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-detail\">\n    <!-- 顶部导航 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"back-btn\">\n          返回列表\n        </el-button>\n        <h2>{{ comic.title }}</h2>\n      </div>\n    </el-header>\n\n    <!-- 主要内容 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画信息区 -->\n        <div class=\"comic-info-section\">\n          <div class=\"comic-cover-large\">\n            <img :src=\"comic.cover\" :alt=\"comic.title\" />\n          </div>\n          <div class=\"comic-details\">\n            <h1 class=\"comic-title\">{{ comic.title }}</h1>\n            <div class=\"comic-meta\">\n              <p><strong>作者:</strong> {{ comic.author }}</p>\n              <p><strong>状态:</strong>\n                <el-tag :type=\"comic.status === '连载中' ? 'success' : 'info'\">\n                  {{ comic.status }}\n                </el-tag>\n              </p>\n              <p><strong>最新章节:</strong> {{ comic.latestChapter }}</p>\n              <p><strong>更新时间:</strong> {{ comic.updateTime }}</p>\n            </div>\n            <div class=\"comic-description\">\n              <h3>简介</h3>\n              <p>{{ comic.description }}</p>\n            </div>\n            <div class=\"action-buttons\">\n              <el-button type=\"primary\" size=\"large\" @click=\"startReading\">\n                开始阅读\n              </el-button>\n              <el-button size=\"large\">收藏</el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 章节列表区 -->\n        <div class=\"chapters-section\">\n          <div class=\"section-header\">\n            <h3>章节列表</h3>\n            <div class=\"sort-controls\">\n              <el-radio-group v-model=\"sortOrder\" @change=\"sortChapters\">\n                <el-radio-button label=\"asc\">正序</el-radio-button>\n                <el-radio-button label=\"desc\">倒序</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n\n          <div class=\"chapters-grid\">\n            <div\n              v-for=\"chapter in sortedChapters\"\n              :key=\"chapter.id\"\n              class=\"chapter-item\"\n              @click=\"readChapter(chapter.id)\"\n            >\n              <div class=\"chapter-number\">{{ chapter.number }}</div>\n              <div class=\"chapter-title\">{{ chapter.title }}</div>\n              <div class=\"chapter-date\">{{ chapter.date }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicDetail',\n  props: ['id'],\n  data() {\n    return {\n      sortOrder: 'desc',\n      comic: {\n        id: 1,\n        title: '进击的巨人',\n        author: '諫山創',\n        status: '已完结',\n        cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center',\n        latestChapter: '第139话',\n        updateTime: '2024-01-15',\n        description: '《进击的巨人》是日本漫画家諫山創创作的漫画作品。故事讲述了人类与巨人之间的战斗，以及主人公艾伦·耶格尔等人为了人类的自由而奋斗的故事。作品以其独特的世界观、复杂的剧情和深刻的主题而广受好评。这部作品不仅在日本国内获得了巨大成功，在全球范围内也拥有大量粉丝，被誉为21世纪最优秀的漫画作品之一。'\n      },\n      chapters: [\n        { id: 1, number: '第1话', title: '致两千年后的你', date: '2023-01-01' },\n        { id: 2, number: '第2话', title: '那一天', date: '2023-01-02' },\n        { id: 3, number: '第3话', title: '解散式之夜', date: '2023-01-03' },\n        { id: 4, number: '第4话', title: '初阵', date: '2023-01-04' },\n        { id: 5, number: '第5话', title: '心脏的跳动声', date: '2023-01-05' },\n        { id: 6, number: '第6话', title: '少女所见的世界', date: '2023-01-06' },\n        { id: 7, number: '第7话', title: '小小的刀刃', date: '2023-01-07' },\n        { id: 8, number: '第8话', title: '咆哮', date: '2023-01-08' },\n        { id: 9, number: '第9话', title: '左臂的去向', date: '2023-01-09' },\n        { id: 10, number: '第10话', title: '应对', date: '2023-01-10' }\n      ]\n    }\n  },\n  computed: {\n    sortedChapters() {\n      const chapters = [...this.chapters]\n      if (this.sortOrder === 'asc') {\n        return chapters.sort((a, b) => a.id - b.id)\n      } else {\n        return chapters.sort((a, b) => b.id - a.id)\n      }\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    startReading() {\n      // 开始阅读第一章\n      const firstChapter = this.sortOrder === 'asc' ?\n        Math.min(...this.chapters.map(c => c.id)) :\n        Math.max(...this.chapters.map(c => c.id))\n      this.readChapter(firstChapter)\n    },\n    readChapter(chapterId) {\n      this.$router.push({\n        name: 'ComicReader',\n        params: {\n          id: this.id,\n          chapterId: chapterId\n        }\n      })\n    },\n    sortChapters() {\n      // 排序逻辑已在computed中处理\n    }\n  },\n  mounted() {\n    // 根据路由参数加载对应的漫画数据\n    console.log('Comic ID:', this.id)\n  }\n}\n</script>\n\n<style scoped>\n.comic-detail {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  gap: 20px;\n}\n\n.back-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.back-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-info-section {\n  display: flex;\n  gap: 30px;\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.comic-cover-large {\n  flex-shrink: 0;\n}\n\n.comic-cover-large img {\n  width: 300px;\n  height: 400px;\n  object-fit: cover;\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-details {\n  flex: 1;\n}\n\n.comic-title {\n  font-size: 32px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.comic-meta {\n  margin-bottom: 25px;\n}\n\n.comic-meta p {\n  margin-bottom: 10px;\n  font-size: 16px;\n  color: #666;\n}\n\n.comic-description {\n  margin-bottom: 30px;\n}\n\n.comic-description h3 {\n  font-size: 18px;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.comic-description p {\n  line-height: 1.6;\n  color: #666;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n}\n\n.chapters-section {\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.section-header h3 {\n  font-size: 20px;\n  color: #333;\n  margin: 0;\n}\n\n.chapters-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 15px;\n}\n\n.chapter-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border: 1px solid #eee;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.chapter-item:hover {\n  background-color: #f8f9fa;\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.chapter-number {\n  font-weight: bold;\n  color: #667eea;\n  min-width: 80px;\n}\n\n.chapter-title {\n  flex: 1;\n  margin-left: 15px;\n  color: #333;\n}\n\n.chapter-date {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .header {\n    height: auto;\n    min-height: 60px;\n  }\n\n  .header-content {\n    padding: 0 15px;\n    height: 60px;\n  }\n\n  .back-btn {\n    font-size: 14px;\n  }\n\n  .header-content h2 {\n    font-size: 16px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    max-width: 200px;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .comic-info-section {\n    flex-direction: column;\n    padding: 20px;\n    gap: 20px;\n  }\n\n  .comic-cover-large {\n    align-self: center;\n  }\n\n  .comic-cover-large img {\n    width: 180px;\n    height: 250px;\n    margin: 0 auto;\n    display: block;\n  }\n\n  .comic-title {\n    font-size: 22px;\n    text-align: center;\n    margin-bottom: 15px;\n  }\n\n  .comic-meta {\n    text-align: center;\n    margin-bottom: 20px;\n  }\n\n  .comic-meta p {\n    font-size: 15px;\n    margin-bottom: 8px;\n  }\n\n  .comic-description {\n    margin-bottom: 25px;\n  }\n\n  .comic-description h3 {\n    font-size: 16px;\n    text-align: center;\n  }\n\n  .comic-description p {\n    font-size: 14px;\n    line-height: 1.6;\n    text-align: justify;\n  }\n\n  .action-buttons {\n    justify-content: center;\n    gap: 10px;\n  }\n\n  .chapters-section {\n    padding: 20px;\n  }\n\n  .section-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: center;\n    text-align: center;\n  }\n\n  .chapters-grid {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n\n  .chapter-item {\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .chapter-number {\n    min-width: 70px;\n    font-size: 14px;\n  }\n\n  .chapter-title {\n    font-size: 14px;\n    margin-left: 12px;\n  }\n\n  .chapter-date {\n    font-size: 11px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    padding: 0 12px;\n    height: 50px;\n  }\n\n  .header-content h2 {\n    font-size: 14px;\n    max-width: 150px;\n  }\n\n  .back-btn {\n    font-size: 13px;\n  }\n\n  .main-content {\n    padding: 12px;\n  }\n\n  .comic-info-section {\n    padding: 15px;\n    gap: 15px;\n  }\n\n  .comic-cover-large img {\n    width: 150px;\n    height: 210px;\n  }\n\n  .comic-title {\n    font-size: 18px;\n    margin-bottom: 12px;\n  }\n\n  .comic-meta p {\n    font-size: 14px;\n    margin-bottom: 6px;\n  }\n\n  .comic-description {\n    margin-bottom: 20px;\n  }\n\n  .comic-description h3 {\n    font-size: 15px;\n    margin-bottom: 8px;\n  }\n\n  .comic-description p {\n    font-size: 13px;\n    line-height: 1.5;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .action-buttons .el-button {\n    width: 100%;\n    padding: 12px;\n  }\n\n  .chapters-section {\n    padding: 15px;\n  }\n\n  .section-header h3 {\n    font-size: 16px;\n  }\n\n  .chapters-grid {\n    gap: 10px;\n  }\n\n  .chapter-item {\n    padding: 12px;\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .chapter-number {\n    min-width: auto;\n    font-weight: bold;\n    color: #667eea;\n  }\n\n  .chapter-title {\n    margin-left: 0;\n    font-size: 13px;\n    color: #333;\n  }\n\n  .chapter-date {\n    margin-left: 0;\n    font-size: 10px;\n    color: #999;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .header-content {\n    padding: 0 10px;\n  }\n\n  .header-content h2 {\n    font-size: 13px;\n    max-width: 120px;\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n\n  .comic-info-section {\n    padding: 12px;\n  }\n\n  .comic-cover-large img {\n    width: 130px;\n    height: 180px;\n  }\n\n  .comic-title {\n    font-size: 16px;\n  }\n\n  .comic-meta p {\n    font-size: 13px;\n  }\n\n  .comic-description p {\n    font-size: 12px;\n  }\n\n  .chapters-section {\n    padding: 12px;\n  }\n\n  .chapter-item {\n    padding: 10px;\n  }\n}\n</style>\n"]}]}