{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748420208499}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicList.vue"], "names": [], "mappings": ";AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ComicList.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div \n            v-for=\"comic in filteredComics\" \n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      totalComics: 0,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://via.placeholder.com/200x280/4CAF50/white?text=进击的巨人',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://via.placeholder.com/200x280/2196F3/white?text=鬼灭之刃',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://via.placeholder.com/200x280/FF9800/white?text=海贼王',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://via.placeholder.com/200x280/9C27B0/white?text=火影忍者',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://via.placeholder.com/200x280/F44336/white?text=龙珠',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://via.placeholder.com/200x280/607D8B/white?text=死神',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic => \n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      this.totalComics = filtered.length\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n}\n\n.search-box .el-input {\n  flex: 1;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 10px;\n    gap: 10px;\n  }\n  \n  .search-box {\n    margin: 0;\n    max-width: 100%;\n  }\n  \n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 15px;\n  }\n  \n  .comic-cover {\n    height: 200px;\n  }\n  \n  .comic-info {\n    padding: 10px;\n  }\n  \n  .comic-title {\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 480px) {\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .main-content {\n    padding: 10px;\n  }\n}\n</style>\n"]}]}