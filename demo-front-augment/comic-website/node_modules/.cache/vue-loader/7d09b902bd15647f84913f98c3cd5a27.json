{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--13-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=template&id=211f044f&scoped=true", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748422088096}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1748420070797}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "placeholder", "clearable", "on", "keyup", "$event", "type", "indexOf", "_k", "keyCode", "key", "searchComics", "apply", "arguments", "model", "value", "searchKeyword", "callback", "$$v", "expression", "icon", "click", "_l", "filteredComics", "comic", "id", "goToDetail", "src", "cover", "alt", "title", "size", "_s", "latestChapter", "updateTime", "currentPage", "pageSize", "layout", "total", "totalFilteredComics", "handleSizeChange", "handleCurrentChange", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"comic-list\" },\n    [\n      _c(\"el-header\", { staticClass: \"header\" }, [\n        _c(\"div\", { staticClass: \"header-content\" }, [\n          _c(\"div\", { staticClass: \"logo\" }, [_c(\"h2\", [_vm._v(\"漫画网站\")])]),\n          _c(\n            \"div\",\n            { staticClass: \"search-box\" },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  placeholder: \"搜索漫画...\",\n                  \"prefix-icon\": \"el-icon-search\",\n                  clearable: \"\",\n                },\n                on: {\n                  keyup: function ($event) {\n                    if (\n                      !$event.type.indexOf(\"key\") &&\n                      _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                    )\n                      return null\n                    return _vm.searchComics.apply(null, arguments)\n                  },\n                },\n                model: {\n                  value: _vm.searchKeyword,\n                  callback: function ($$v) {\n                    _vm.searchKeyword = $$v\n                  },\n                  expression: \"searchKeyword\",\n                },\n              }),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\", icon: \"el-icon-search\" },\n                  on: { click: _vm.searchComics },\n                },\n                [_vm._v(\"搜索\")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"user-actions\" },\n            [\n              _c(\"el-button\", { attrs: { type: \"text\" } }, [_vm._v(\"登录\")]),\n              _c(\"el-button\", { attrs: { type: \"primary\" } }, [_vm._v(\"注册\")]),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _c(\"el-main\", { staticClass: \"main-content\" }, [\n        _c(\"div\", { staticClass: \"content-wrapper\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"comic-grid\" },\n            _vm._l(_vm.filteredComics, function (comic) {\n              return _c(\n                \"div\",\n                {\n                  key: comic.id,\n                  staticClass: \"comic-card\",\n                  on: {\n                    click: function ($event) {\n                      return _vm.goToDetail(comic.id)\n                    },\n                  },\n                },\n                [\n                  _c(\"div\", { staticClass: \"comic-cover\" }, [\n                    _c(\"img\", {\n                      attrs: { src: comic.cover, alt: comic.title },\n                    }),\n                    _c(\n                      \"div\",\n                      { staticClass: \"comic-overlay\" },\n                      [\n                        _c(\n                          \"el-button\",\n                          { attrs: { type: \"primary\", size: \"small\" } },\n                          [_vm._v(\"查看详情\")]\n                        ),\n                      ],\n                      1\n                    ),\n                  ]),\n                  _c(\"div\", { staticClass: \"comic-info\" }, [\n                    _c(\"h3\", { staticClass: \"comic-title\" }, [\n                      _vm._v(_vm._s(comic.title)),\n                    ]),\n                    _c(\"p\", { staticClass: \"comic-latest\" }, [\n                      _vm._v(\"最新: \" + _vm._s(comic.latestChapter)),\n                    ]),\n                    _c(\"p\", { staticClass: \"comic-update\" }, [\n                      _vm._v(_vm._s(comic.updateTime)),\n                    ]),\n                  ]),\n                ]\n              )\n            }),\n            0\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"pagination-wrapper\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"current-page\": _vm.currentPage,\n                  \"page-sizes\": [12, 24, 36, 48],\n                  \"page-size\": _vm.pageSize,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.totalFilteredComics,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.handleCurrentChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ]),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CAAC,WAAW,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAChEH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLC,WAAW,EAAE,SAAS;MACtB,aAAa,EAAE,gBAAgB;MAC/BC,SAAS,EAAE;IACb,CAAC;IACDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACC,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,IAC3BZ,GAAG,CAACa,EAAE,CAACH,MAAM,CAACI,OAAO,EAAE,OAAO,EAAE,EAAE,EAAEJ,MAAM,CAACK,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAOf,GAAG,CAACgB,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAChD;IACF,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAEpB,GAAG,CAACqB,aAAa;MACxBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBvB,GAAG,CAACqB,aAAa,GAAGE,GAAG;MACzB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFvB,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEM,IAAI,EAAE,SAAS;MAAEc,IAAI,EAAE;IAAiB,CAAC;IAClDjB,EAAE,EAAE;MAAEkB,KAAK,EAAE1B,GAAG,CAACgB;IAAa;EAChC,CAAC,EACD,CAAChB,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,EACDH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAO;EAAE,CAAC,EAAE,CAACX,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC5DH,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAU;EAAE,CAAC,EAAE,CAACX,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAChE,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7BH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,cAAc,EAAE,UAAUC,KAAK,EAAE;IAC1C,OAAO5B,EAAE,CACP,KAAK,EACL;MACEc,GAAG,EAAEc,KAAK,CAACC,EAAE;MACb3B,WAAW,EAAE,YAAY;MACzBK,EAAE,EAAE;QACFkB,KAAK,EAAE,SAAAA,CAAUhB,MAAM,EAAE;UACvB,OAAOV,GAAG,CAAC+B,UAAU,CAACF,KAAK,CAACC,EAAE,CAAC;QACjC;MACF;IACF,CAAC,EACD,CACE7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QAAE2B,GAAG,EAAEH,KAAK,CAACI,KAAK;QAAEC,GAAG,EAAEL,KAAK,CAACM;MAAM;IAC9C,CAAC,CAAC,EACFlC,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAChC,CACEF,EAAE,CACA,WAAW,EACX;MAAEI,KAAK,EAAE;QAAEM,IAAI,EAAE,SAAS;QAAEyB,IAAI,EAAE;MAAQ;IAAE,CAAC,EAC7C,CAACpC,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACvCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACqC,EAAE,CAACR,KAAK,CAACM,KAAK,CAAC,CAAC,CAC5B,CAAC,EACFlC,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACvCH,GAAG,CAACI,EAAE,CAAC,MAAM,GAAGJ,GAAG,CAACqC,EAAE,CAACR,KAAK,CAACS,aAAa,CAAC,CAAC,CAC7C,CAAC,EACFrC,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACvCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACqC,EAAE,CAACR,KAAK,CAACU,UAAU,CAAC,CAAC,CACjC,CAAC,CACH,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDtC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,eAAe,EAAE;IAClBI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACwC,WAAW;MAC/B,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9B,WAAW,EAAExC,GAAG,CAACyC,QAAQ;MACzBC,MAAM,EAAE,yCAAyC;MACjDC,KAAK,EAAE3C,GAAG,CAAC4C;IACb,CAAC;IACDpC,EAAE,EAAE;MACF,aAAa,EAAER,GAAG,CAAC6C,gBAAgB;MACnC,gBAAgB,EAAE7C,GAAG,CAAC8C;IACxB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBhD,MAAM,CAACiD,aAAa,GAAG,IAAI;AAE3B,SAASjD,MAAM,EAAEgD,eAAe", "ignoreList": []}]}