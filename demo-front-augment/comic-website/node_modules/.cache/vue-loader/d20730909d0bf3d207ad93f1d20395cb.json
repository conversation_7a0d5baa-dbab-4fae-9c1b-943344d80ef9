{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--13-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=template&id=7f5f6fb4&scoped=true", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748421647283}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1748420070797}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "fullscreen", "isFullscreen", "hidden", "controlsHidden", "attrs", "icon", "type", "on", "click", "goBack", "_v", "_s", "currentChapter", "title", "readingMode", "size", "$event", "setReadingMode", "toggleFullscreen", "toggleControls", "touchstart", "handleTouchStart", "touchend", "handleTouchEnd", "touchmove", "handleTouchMove", "ref", "_l", "images", "image", "index", "key", "active", "currentPage", "src", "alt", "load", "onImageLoad", "error", "onImageError", "circle", "stopPropagation", "prevPage", "apply", "arguments", "_e", "length", "nextPage", "disabled", "hasPrevChapter", "prevChapter", "staticStyle", "width", "change", "changeChapter", "model", "value", "currentChapterId", "callback", "$$v", "expression", "allChapters", "chapter", "id", "label", "hasNextChapter", "nextChapter", "goHome", "loading", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"comic-reader\", class: { fullscreen: _vm.isFullscreen } },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"top-controls\", class: { hidden: _vm.controlsHidden } },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"controls-left\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"control-btn\",\n                  attrs: { icon: \"el-icon-arrow-left\", type: \"text\" },\n                  on: { click: _vm.goBack },\n                },\n                [_vm._v(\" 返回 \")]\n              ),\n              _c(\"span\", { staticClass: \"chapter-info\" }, [\n                _vm._v(_vm._s(_vm.currentChapter.title)),\n              ]),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"controls-right\" },\n            [\n              _c(\n                \"el-button-group\",\n                { staticClass: \"reading-mode-toggle\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: _vm.readingMode === \"horizontal\" ? \"primary\" : \"\",\n                        size: \"small\",\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.setReadingMode(\"horizontal\")\n                        },\n                      },\n                    },\n                    [_vm._v(\" 左右滑动 \")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: _vm.readingMode === \"vertical\" ? \"primary\" : \"\",\n                        size: \"small\",\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.setReadingMode(\"vertical\")\n                        },\n                      },\n                    },\n                    [_vm._v(\" 上下拼接 \")]\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"control-btn\",\n                  attrs: {\n                    icon: _vm.isFullscreen\n                      ? \"el-icon-copy-document\"\n                      : \"el-icon-full-screen\",\n                    type: \"text\",\n                  },\n                  on: { click: _vm.toggleFullscreen },\n                },\n                [\n                  _vm._v(\n                    \" \" + _vm._s(_vm.isFullscreen ? \"退出全屏\" : \"全屏\") + \" \"\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n      _c(\n        \"div\",\n        {\n          staticClass: \"reading-area\",\n          on: {\n            click: _vm.toggleControls,\n            touchstart: _vm.handleTouchStart,\n            touchend: _vm.handleTouchEnd,\n            touchmove: _vm.handleTouchMove,\n          },\n        },\n        [\n          _vm.readingMode === \"horizontal\"\n            ? _c(\"div\", { staticClass: \"horizontal-reader\" }, [\n                _c(\n                  \"div\",\n                  { ref: \"imageContainer\", staticClass: \"image-container\" },\n                  _vm._l(_vm.currentChapter.images, function (image, index) {\n                    return _c(\"img\", {\n                      key: index,\n                      staticClass: \"comic-image\",\n                      class: { active: index === _vm.currentPage },\n                      attrs: { src: image, alt: `第${_vm.currentPage + 1}页` },\n                      on: { load: _vm.onImageLoad, error: _vm.onImageError },\n                    })\n                  }),\n                  0\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"nav-buttons\" },\n                  [\n                    _vm.currentPage > 0\n                      ? _c(\"el-button\", {\n                          staticClass: \"nav-btn nav-btn-left\",\n                          attrs: { icon: \"el-icon-arrow-left\", circle: \"\" },\n                          on: {\n                            click: function ($event) {\n                              $event.stopPropagation()\n                              return _vm.prevPage.apply(null, arguments)\n                            },\n                          },\n                        })\n                      : _vm._e(),\n                    _vm.currentPage < _vm.currentChapter.images.length - 1\n                      ? _c(\"el-button\", {\n                          staticClass: \"nav-btn nav-btn-right\",\n                          attrs: { icon: \"el-icon-arrow-right\", circle: \"\" },\n                          on: {\n                            click: function ($event) {\n                              $event.stopPropagation()\n                              return _vm.nextPage.apply(null, arguments)\n                            },\n                          },\n                        })\n                      : _vm._e(),\n                  ],\n                  1\n                ),\n              ])\n            : _c(\n                \"div\",\n                { ref: \"verticalReader\", staticClass: \"vertical-reader\" },\n                _vm._l(_vm.currentChapter.images, function (image, index) {\n                  return _c(\"img\", {\n                    key: index,\n                    staticClass: \"comic-image-vertical\",\n                    attrs: { src: image, alt: `第${index + 1}页` },\n                    on: { load: _vm.onImageLoad, error: _vm.onImageError },\n                  })\n                }),\n                0\n              ),\n        ]\n      ),\n      _c(\n        \"div\",\n        {\n          staticClass: \"bottom-controls\",\n          class: { hidden: _vm.controlsHidden },\n        },\n        [\n          _vm.readingMode === \"horizontal\"\n            ? _c(\"div\", { staticClass: \"page-info\" }, [\n                _c(\"span\", [\n                  _vm._v(\n                    _vm._s(_vm.currentPage + 1) +\n                      \" / \" +\n                      _vm._s(_vm.currentChapter.images.length)\n                  ),\n                ]),\n              ])\n            : _vm._e(),\n          _c(\n            \"div\",\n            { staticClass: \"chapter-navigation\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { disabled: !_vm.hasPrevChapter, size: \"small\" },\n                  on: { click: _vm.prevChapter },\n                },\n                [_vm._v(\" 上一章 \")]\n              ),\n              _c(\n                \"el-select\",\n                {\n                  staticStyle: { width: \"200px\" },\n                  attrs: { size: \"small\" },\n                  on: { change: _vm.changeChapter },\n                  model: {\n                    value: _vm.currentChapterId,\n                    callback: function ($$v) {\n                      _vm.currentChapterId = $$v\n                    },\n                    expression: \"currentChapterId\",\n                  },\n                },\n                _vm._l(_vm.allChapters, function (chapter) {\n                  return _c(\"el-option\", {\n                    key: chapter.id,\n                    attrs: { label: chapter.title, value: chapter.id },\n                  })\n                }),\n                1\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { disabled: !_vm.hasNextChapter, size: \"small\" },\n                  on: { click: _vm.nextChapter },\n                },\n                [_vm._v(\" 下一章 \")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"quick-actions\" },\n            [\n              _c(\n                \"el-button\",\n                { attrs: { size: \"small\" }, on: { click: _vm.goHome } },\n                [_vm._v(\"回到首页\")]\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n      _vm.loading\n        ? _c(\n            \"div\",\n            { staticClass: \"loading-overlay\" },\n            [_c(\"el-loading-text\", [_vm._v(\"加载中...\")])],\n            1\n          )\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE,cAAc;IAAEC,KAAK,EAAE;MAAEC,UAAU,EAAEL,GAAG,CAACM;IAAa;EAAE,CAAC,EACxE,CACEL,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,cAAc;IAAEC,KAAK,EAAE;MAAEG,MAAM,EAAEP,GAAG,CAACQ;IAAe;EAAE,CAAC,EACtE,CACEP,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BM,KAAK,EAAE;MAAEC,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAO,CAAC;IACnDC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACc;IAAO;EAC1B,CAAC,EACD,CAACd,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDd,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,cAAc,CAACC,KAAK,CAAC,CAAC,CACzC,CAAC,CACH,EACD,CACF,CAAC,EACDjB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,iBAAiB,EACjB;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,WAAW,EACX;IACEQ,KAAK,EAAE;MACLE,IAAI,EAAEX,GAAG,CAACmB,WAAW,KAAK,YAAY,GAAG,SAAS,GAAG,EAAE;MACvDC,IAAI,EAAE;IACR,CAAC;IACDR,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUQ,MAAM,EAAE;QACvB,OAAOrB,GAAG,CAACsB,cAAc,CAAC,YAAY,CAAC;MACzC;IACF;EACF,CAAC,EACD,CAACtB,GAAG,CAACe,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDd,EAAE,CACA,WAAW,EACX;IACEQ,KAAK,EAAE;MACLE,IAAI,EAAEX,GAAG,CAACmB,WAAW,KAAK,UAAU,GAAG,SAAS,GAAG,EAAE;MACrDC,IAAI,EAAE;IACR,CAAC;IACDR,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUQ,MAAM,EAAE;QACvB,OAAOrB,GAAG,CAACsB,cAAc,CAAC,UAAU,CAAC;MACvC;IACF;EACF,CAAC,EACD,CAACtB,GAAG,CAACe,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,EACD,CACF,CAAC,EACDd,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BM,KAAK,EAAE;MACLC,IAAI,EAAEV,GAAG,CAACM,YAAY,GAClB,uBAAuB,GACvB,qBAAqB;MACzBK,IAAI,EAAE;IACR,CAAC;IACDC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACuB;IAAiB;EACpC,CAAC,EACD,CACEvB,GAAG,CAACe,EAAE,CACJ,GAAG,GAAGf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACM,YAAY,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,GACnD,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACDL,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,cAAc;IAC3BS,EAAE,EAAE;MACFC,KAAK,EAAEb,GAAG,CAACwB,cAAc;MACzBC,UAAU,EAAEzB,GAAG,CAAC0B,gBAAgB;MAChCC,QAAQ,EAAE3B,GAAG,CAAC4B,cAAc;MAC5BC,SAAS,EAAE7B,GAAG,CAAC8B;IACjB;EACF,CAAC,EACD,CACE9B,GAAG,CAACmB,WAAW,KAAK,YAAY,GAC5BlB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CACA,KAAK,EACL;IAAE8B,GAAG,EAAE,gBAAgB;IAAE5B,WAAW,EAAE;EAAkB,CAAC,EACzDH,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACiB,cAAc,CAACgB,MAAM,EAAE,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACxD,OAAOlC,EAAE,CAAC,KAAK,EAAE;MACfmC,GAAG,EAAED,KAAK;MACVhC,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE;QAAEiC,MAAM,EAAEF,KAAK,KAAKnC,GAAG,CAACsC;MAAY,CAAC;MAC5C7B,KAAK,EAAE;QAAE8B,GAAG,EAAEL,KAAK;QAAEM,GAAG,EAAE,IAAIxC,GAAG,CAACsC,WAAW,GAAG,CAAC;MAAI,CAAC;MACtD1B,EAAE,EAAE;QAAE6B,IAAI,EAAEzC,GAAG,CAAC0C,WAAW;QAAEC,KAAK,EAAE3C,GAAG,CAAC4C;MAAa;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACD3C,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEH,GAAG,CAACsC,WAAW,GAAG,CAAC,GACfrC,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,sBAAsB;IACnCM,KAAK,EAAE;MAAEC,IAAI,EAAE,oBAAoB;MAAEmC,MAAM,EAAE;IAAG,CAAC;IACjDjC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUQ,MAAM,EAAE;QACvBA,MAAM,CAACyB,eAAe,CAAC,CAAC;QACxB,OAAO9C,GAAG,CAAC+C,QAAQ,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC5C;IACF;EACF,CAAC,CAAC,GACFjD,GAAG,CAACkD,EAAE,CAAC,CAAC,EACZlD,GAAG,CAACsC,WAAW,GAAGtC,GAAG,CAACiB,cAAc,CAACgB,MAAM,CAACkB,MAAM,GAAG,CAAC,GAClDlD,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,uBAAuB;IACpCM,KAAK,EAAE;MAAEC,IAAI,EAAE,qBAAqB;MAAEmC,MAAM,EAAE;IAAG,CAAC;IAClDjC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUQ,MAAM,EAAE;QACvBA,MAAM,CAACyB,eAAe,CAAC,CAAC;QACxB,OAAO9C,GAAG,CAACoD,QAAQ,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC5C;IACF;EACF,CAAC,CAAC,GACFjD,GAAG,CAACkD,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,CAAC,GACFjD,EAAE,CACA,KAAK,EACL;IAAE8B,GAAG,EAAE,gBAAgB;IAAE5B,WAAW,EAAE;EAAkB,CAAC,EACzDH,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACiB,cAAc,CAACgB,MAAM,EAAE,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACxD,OAAOlC,EAAE,CAAC,KAAK,EAAE;MACfmC,GAAG,EAAED,KAAK;MACVhC,WAAW,EAAE,sBAAsB;MACnCM,KAAK,EAAE;QAAE8B,GAAG,EAAEL,KAAK;QAAEM,GAAG,EAAE,IAAIL,KAAK,GAAG,CAAC;MAAI,CAAC;MAC5CvB,EAAE,EAAE;QAAE6B,IAAI,EAAEzC,GAAG,CAAC0C,WAAW;QAAEC,KAAK,EAAE3C,GAAG,CAAC4C;MAAa;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CAET,CAAC,EACD3C,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;MAAEG,MAAM,EAAEP,GAAG,CAACQ;IAAe;EACtC,CAAC,EACD,CACER,GAAG,CAACmB,WAAW,KAAK,YAAY,GAC5BlB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACe,EAAE,CACJf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACsC,WAAW,GAAG,CAAC,CAAC,GACzB,KAAK,GACLtC,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,cAAc,CAACgB,MAAM,CAACkB,MAAM,CAC3C,CAAC,CACF,CAAC,CACH,CAAC,GACFnD,GAAG,CAACkD,EAAE,CAAC,CAAC,EACZjD,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,WAAW,EACX;IACEQ,KAAK,EAAE;MAAE4C,QAAQ,EAAE,CAACrD,GAAG,CAACsD,cAAc;MAAElC,IAAI,EAAE;IAAQ,CAAC;IACvDR,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACuD;IAAY;EAC/B,CAAC,EACD,CAACvD,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,EACDd,EAAE,CACA,WAAW,EACX;IACEuD,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAC;IAC/BhD,KAAK,EAAE;MAAEW,IAAI,EAAE;IAAQ,CAAC;IACxBR,EAAE,EAAE;MAAE8C,MAAM,EAAE1D,GAAG,CAAC2D;IAAc,CAAC;IACjCC,KAAK,EAAE;MACLC,KAAK,EAAE7D,GAAG,CAAC8D,gBAAgB;MAC3BC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBhE,GAAG,CAAC8D,gBAAgB,GAAGE,GAAG;MAC5B,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACDjE,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACkE,WAAW,EAAE,UAAUC,OAAO,EAAE;IACzC,OAAOlE,EAAE,CAAC,WAAW,EAAE;MACrBmC,GAAG,EAAE+B,OAAO,CAACC,EAAE;MACf3D,KAAK,EAAE;QAAE4D,KAAK,EAAEF,OAAO,CAACjD,KAAK;QAAE2C,KAAK,EAAEM,OAAO,CAACC;MAAG;IACnD,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACDnE,EAAE,CACA,WAAW,EACX;IACEQ,KAAK,EAAE;MAAE4C,QAAQ,EAAE,CAACrD,GAAG,CAACsE,cAAc;MAAElD,IAAI,EAAE;IAAQ,CAAC;IACvDR,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACuE;IAAY;EAC/B,CAAC,EACD,CAACvE,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,CACF,EACD,CACF,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,WAAW,EACX;IAAEQ,KAAK,EAAE;MAAEW,IAAI,EAAE;IAAQ,CAAC;IAAER,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACwE;IAAO;EAAE,CAAC,EACvD,CAACxE,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACDf,GAAG,CAACyE,OAAO,GACPxE,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CAACF,EAAE,CAAC,iBAAiB,EAAE,CAACD,GAAG,CAACe,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC3C,CACF,CAAC,GACDf,GAAG,CAACkD,EAAE,CAAC,CAAC,CAEhB,CAAC;AACH,CAAC;AACD,IAAIwB,eAAe,GAAG,EAAE;AACxB3E,MAAM,CAAC4E,aAAa,GAAG,IAAI;AAE3B,SAAS5E,MAAM,EAAE2E,eAAe", "ignoreList": []}]}