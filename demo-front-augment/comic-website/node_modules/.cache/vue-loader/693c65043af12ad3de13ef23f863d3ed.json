{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue?vue&type=style&index=0&id=7ba5bd90&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/App.vue", "mtime": 1748421685661}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>+iJsuaooeW8j+agt+W8jyAqLwp9CgovKiDlh4/lsJHliqjnlLvnmoTnlKjmiLflgY/lpb0gKi8KQG1lZGlhIChwcmVmZXJzLXJlZHVjZWQtbW90aW9uOiByZWR1Y2UpIHsKICAqIHsKICAgIGFuaW1hdGlvbi1kdXJhdGlvbjogMC4wMW1zICFpbXBvcnRhbnQ7CiAgICBhbmltYXRpb24taXRlcmF0aW9uLWNvdW50OiAxICFpbXBvcnRhbnQ7CiAgICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAwLjAxbXMgIWltcG9ydGFudDsKICB9Cn0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAaA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml {\n  /* 防止iOS Safari双击缩放 */\n  touch-action: manipulation;\n  /* 优化移动端滚动 */\n  -webkit-overflow-scrolling: touch;\n}\n\nbody {\n  background-color: #f5f5f5;\n  /* 防止移动端橡皮筋效果 */\n  overscroll-behavior: none;\n  /* 优化字体渲染 */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  /* 防止移动端文本选择 */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n#app {\n  font-family: 'Avenir', Helvetica, Arial, sans-serif, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n}\n\n/* 移动端输入框优化 */\ninput, textarea, select {\n  -webkit-user-select: text;\n  -moz-user-select: text;\n  -ms-user-select: text;\n  user-select: text;\n}\n\n/* Element UI 移动端优化 */\n.el-button {\n  /* 增加触摸区域 */\n  min-height: 36px;\n  /* 防止双击缩放 */\n  touch-action: manipulation;\n}\n\n.el-input__inner {\n  /* 防止iOS输入框缩放 */\n  font-size: 16px;\n  /* 优化移动端输入体验 */\n  -webkit-appearance: none;\n  border-radius: 4px;\n}\n\n.el-select .el-input__inner {\n  cursor: pointer;\n}\n\n/* 分页组件移动端优化 */\n.el-pagination {\n  text-align: center;\n}\n\n.el-pagination .el-pager li {\n  min-width: 32px;\n  height: 32px;\n  line-height: 32px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .el-container {\n    flex-direction: column;\n  }\n\n  /* Element UI 组件移动端调整 */\n  .el-button {\n    min-height: 40px;\n    padding: 8px 15px;\n  }\n\n  .el-button--small {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-select .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n\n  /* 优化弹窗在移动端的显示 */\n  .el-dialog {\n    width: 90% !important;\n    margin: 5vh auto !important;\n  }\n\n  .el-message-box {\n    width: 90% !important;\n  }\n}\n\n@media (max-width: 480px) {\n  /* 超小屏幕优化 */\n  .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n    padding: 10px 15px;\n    font-size: 14px;\n  }\n\n  .el-button--small {\n    min-height: 40px;\n    padding: 8px 12px;\n    font-size: 13px;\n  }\n\n  .el-input__inner {\n    height: 44px;\n    line-height: 44px;\n    font-size: 16px; /* 防止iOS缩放 */\n  }\n\n  .el-pagination .el-pager li {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n    margin: 0 2px;\n  }\n\n  .el-pagination .btn-prev,\n  .el-pagination .btn-next {\n    min-width: 36px;\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  /* 横屏时减少垂直空间占用 */\n  .el-button {\n    min-height: 36px;\n    padding: 6px 12px;\n  }\n\n  .el-input__inner {\n    height: 36px;\n    line-height: 36px;\n  }\n}\n\n/* 高分辨率屏幕优化 */\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n  /* 优化高分辨率屏幕的显示效果 */\n  #app {\n    -webkit-font-smoothing: subpixel-antialiased;\n  }\n}\n\n/* 暗色模式支持（预留） */\n@media (prefers-color-scheme: dark) {\n  /* 可以在这里添加暗色模式样式 */\n}\n\n/* 减少动画的用户偏好 */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n</style>\n"]}]}