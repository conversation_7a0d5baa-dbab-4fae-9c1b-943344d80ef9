{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=style&index=0&id=7f5f6fb4&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748421647283}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicReader.vue"], "names": [], "mappings": ";AA6a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file": "ComicReader.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div\n      class=\"reading-area\"\n      @click=\"toggleControls\"\n      @touchstart=\"handleTouchStart\"\n      @touchend=\"handleTouchEnd\"\n      @touchmove=\"handleTouchMove\"\n    >\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      touchStartX: 0,\n      touchStartY: 0,\n      touchStartTime: 0,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    },\n    handleTouchStart(event) {\n      if (event.touches.length === 1) {\n        this.touchStartX = event.touches[0].clientX\n        this.touchStartY = event.touches[0].clientY\n        this.touchStartTime = Date.now()\n      }\n    },\n    handleTouchMove(event) {\n      // 防止页面滚动（仅在水平滑动模式下）\n      if (this.readingMode === 'horizontal' && event.touches.length === 1) {\n        const touchX = event.touches[0].clientX\n        const touchY = event.touches[0].clientY\n        const deltaX = Math.abs(touchX - this.touchStartX)\n        const deltaY = Math.abs(touchY - this.touchStartY)\n\n        // 如果水平滑动距离大于垂直滑动距离，阻止默认滚动\n        if (deltaX > deltaY) {\n          event.preventDefault()\n        }\n      }\n    },\n    handleTouchEnd(event) {\n      if (!this.touchStartX || event.changedTouches.length !== 1) return\n\n      const touchEndX = event.changedTouches[0].clientX\n      const touchEndY = event.changedTouches[0].clientY\n      const touchEndTime = Date.now()\n\n      const deltaX = this.touchStartX - touchEndX\n      const deltaY = this.touchStartY - touchEndY\n      const deltaTime = touchEndTime - this.touchStartTime\n\n      // 检查是否为有效的滑动手势\n      const minSwipeDistance = 50 // 最小滑动距离\n      const maxSwipeTime = 500 // 最大滑动时间\n      const maxVerticalDistance = 100 // 最大垂直偏移\n\n      if (Math.abs(deltaX) > minSwipeDistance &&\n          Math.abs(deltaY) < maxVerticalDistance &&\n          deltaTime < maxSwipeTime) {\n\n        if (this.readingMode === 'horizontal') {\n          // 水平滑动模式下的手势处理\n          if (deltaX > 0) {\n            // 向左滑动，下一页\n            this.nextPage()\n          } else {\n            // 向右滑动，上一页\n            this.prevPage()\n          }\n        }\n      } else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {\n        // 点击手势，切换控制栏显示\n        this.toggleControls()\n      }\n\n      // 重置触摸状态\n      this.touchStartX = 0\n      this.touchStartY = 0\n      this.touchStartTime = 0\n    },\n    handleKeydown(event) {\n      if (this.readingMode === 'horizontal') {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault()\n            this.prevPage()\n            break\n          case 'ArrowRight':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case ' ':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case 'Escape':\n            if (this.isFullscreen) {\n              this.exitFullscreen()\n            }\n            break\n        }\n      }\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 添加键盘事件监听\n    document.addEventListener('keydown', this.handleKeydown)\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 监听webkit全屏状态变化（Safari）\n    document.addEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    document.removeEventListener('keydown', this.handleKeydown)\n    document.removeEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n    document.removeEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .top-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .bottom-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .controls-left {\n    gap: 8px;\n    flex: 1;\n    min-width: 0;\n  }\n\n  .controls-right {\n    gap: 8px;\n    flex-shrink: 0;\n  }\n\n  .control-btn {\n    font-size: 14px;\n    padding: 5px 8px;\n  }\n\n  .chapter-info {\n    font-size: 13px;\n    max-width: 120px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .chapter-navigation {\n    gap: 8px;\n    flex: 1;\n    justify-content: center;\n  }\n\n  .chapter-navigation .el-select {\n    width: 140px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .page-info {\n    font-size: 13px;\n    min-width: 60px;\n  }\n\n  .quick-actions .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .nav-btn {\n    width: 45px;\n    height: 45px;\n    font-size: 16px;\n  }\n\n  .nav-buttons {\n    padding: 0 15px;\n  }\n\n  .vertical-reader {\n    padding: 10px 5px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 3px;\n    max-width: calc(100% - 10px);\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n    padding: 0 10px;\n    flex-wrap: wrap;\n  }\n\n  .controls-left {\n    gap: 6px;\n    order: 1;\n    width: 100%;\n    justify-content: space-between;\n    margin-bottom: 5px;\n  }\n\n  .controls-right {\n    gap: 6px;\n    order: 2;\n    width: 100%;\n    justify-content: center;\n  }\n\n  .control-btn {\n    font-size: 12px;\n    padding: 4px 6px;\n  }\n\n  .chapter-info {\n    font-size: 12px;\n    max-width: 100px;\n    flex: 1;\n  }\n\n  .reading-mode-toggle {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    flex: 1;\n  }\n\n  .bottom-controls {\n    height: auto;\n    min-height: 50px;\n    flex-direction: column;\n    gap: 8px;\n    padding: 8px 10px;\n  }\n\n  .page-info {\n    order: 1;\n    text-align: center;\n    font-size: 12px;\n    min-width: auto;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    gap: 6px;\n    width: 100%;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .chapter-navigation .el-select {\n    width: 120px !important;\n    margin: 0 5px;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n    min-width: 60px;\n  }\n\n  .quick-actions {\n    order: 3;\n    text-align: center;\n    min-width: auto;\n  }\n\n  .quick-actions .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n    font-size: 14px;\n  }\n\n  .nav-buttons {\n    padding: 0 10px;\n  }\n\n  .vertical-reader {\n    padding: 5px 3px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n    max-width: calc(100% - 6px);\n  }\n\n  .loading-overlay {\n    font-size: 16px;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .top-controls {\n    height: 45px;\n    padding: 0 8px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 4px;\n  }\n\n  .control-btn {\n    font-size: 11px;\n    padding: 3px 5px;\n  }\n\n  .chapter-info {\n    font-size: 11px;\n    max-width: 80px;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 2px 4px;\n    font-size: 9px;\n  }\n\n  .bottom-controls {\n    padding: 6px 8px;\n    gap: 6px;\n  }\n\n  .page-info {\n    font-size: 11px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 100px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    min-width: 50px;\n  }\n\n  .quick-actions .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n  }\n\n  .nav-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 12px;\n  }\n\n  .nav-buttons {\n    padding: 0 8px;\n  }\n\n  .vertical-reader {\n    padding: 3px 2px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 1px;\n    max-width: calc(100% - 4px);\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.7;\n    background-color: rgba(0, 0, 0, 0.8) !important;\n  }\n\n  .nav-btn:active {\n    opacity: 1;\n    transform: scale(0.95);\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n\n  /* 增加触摸区域 */\n  .control-btn,\n  .reading-mode-toggle .el-button,\n  .chapter-navigation .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n  }\n\n  /* 防止双击缩放 */\n  .reading-area {\n    touch-action: pan-y pinch-zoom;\n  }\n\n  .horizontal-reader {\n    touch-action: pan-y;\n  }\n\n  /* 优化滚动性能 */\n  .vertical-reader {\n    -webkit-overflow-scrolling: touch;\n    scroll-behavior: smooth;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  .top-controls,\n  .bottom-controls {\n    height: 45px;\n  }\n\n  .top-controls {\n    padding: 0 15px;\n  }\n\n  .bottom-controls {\n    padding: 0 15px;\n    flex-direction: row;\n    justify-content: space-between;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    flex: 1;\n    max-width: 300px;\n  }\n\n  .page-info {\n    order: 1;\n  }\n\n  .quick-actions {\n    order: 3;\n  }\n\n  .vertical-reader {\n    padding: 5px 10px;\n  }\n}\n</style>\n"]}]}