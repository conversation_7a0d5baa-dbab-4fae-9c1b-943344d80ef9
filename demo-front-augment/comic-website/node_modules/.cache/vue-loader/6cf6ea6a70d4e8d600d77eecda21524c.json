{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=style&index=0&id=7f5f6fb4&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748420855536}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/css-loader/dist/cjs.js", "mtime": 1748420067518}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1748420070719}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/postcss-loader/src/index.js", "mtime": 1748420068924}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ComicReader.vue"], "names": [], "mappings": ";AA8TA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ComicReader.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div class=\"reading-area\" @click=\"toggleControls\">\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .top-controls,\n  .bottom-controls {\n    padding: 0 10px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 10px;\n  }\n\n  .chapter-info {\n    font-size: 14px;\n    max-width: 150px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .chapter-navigation {\n    gap: 10px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 150px !important;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n  }\n\n  .vertical-reader {\n    padding: 10px 0;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n  }\n\n  .bottom-controls {\n    height: 50px;\n    flex-direction: column;\n    gap: 5px;\n    padding: 5px 10px;\n  }\n\n  .chapter-navigation {\n    order: 1;\n  }\n\n  .page-info,\n  .quick-actions {\n    order: 2;\n    min-width: auto;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 5px 8px;\n    font-size: 12px;\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.5;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n}\n</style>\n"]}]}