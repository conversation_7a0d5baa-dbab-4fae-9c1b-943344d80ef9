{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue?vue&type=template&id=40ddb842&scoped=true", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicDetail.vue", "mtime": 1748420259114}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1748420070797}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}