{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748421647283}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "data", "readingMode", "currentPage", "currentChapterId", "parseInt", "chapterId", "isFullscreen", "controlsHidden", "loading", "touchStartX", "touchStartY", "touchStartTime", "currentChapter", "id", "title", "images", "allChapters", "computed", "hasPrevChapter", "currentIndex", "findIndex", "c", "hasNextChapter", "length", "methods", "setReadingMode", "mode", "localStorage", "setItem", "toggleControls", "toggleFullscreen", "enterFullscreen", "exitFullscreen", "element", "$el", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "document", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "prevPage", "updateImageDisplay", "nextPage", "nextChapter", "container", "$refs", "imageContainer", "imageWidth", "clientWidth", "scrollLeft", "prevChapter", "changeChapter", "setTimeout", "chapter", "find", "$router", "replace", "params", "goBack", "push", "goHome", "onImageLoad", "onImageError", "console", "error", "handleTouchStart", "event", "touches", "clientX", "clientY", "Date", "now", "handleTouchMove", "touchX", "touchY", "deltaX", "Math", "abs", "deltaY", "preventDefault", "handleTouchEnd", "changedTouches", "touchEndX", "touchEndY", "touchEndTime", "deltaTime", "minSwipeDistance", "maxSwipeTime", "maxVerticalDistance", "handleKeydown", "key", "mounted", "savedMode", "getItem", "addEventListener", "fullscreenElement", "webkitFullscreenElement", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/views/ComicReader.vue"], "sourcesContent": ["<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div\n      class=\"reading-area\"\n      @click=\"toggleControls\"\n      @touchstart=\"handleTouchStart\"\n      @touchend=\"handleTouchEnd\"\n      @touchmove=\"handleTouchMove\"\n    >\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      touchStartX: 0,\n      touchStartY: 0,\n      touchStartTime: 0,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    },\n    handleTouchStart(event) {\n      if (event.touches.length === 1) {\n        this.touchStartX = event.touches[0].clientX\n        this.touchStartY = event.touches[0].clientY\n        this.touchStartTime = Date.now()\n      }\n    },\n    handleTouchMove(event) {\n      // 防止页面滚动（仅在水平滑动模式下）\n      if (this.readingMode === 'horizontal' && event.touches.length === 1) {\n        const touchX = event.touches[0].clientX\n        const touchY = event.touches[0].clientY\n        const deltaX = Math.abs(touchX - this.touchStartX)\n        const deltaY = Math.abs(touchY - this.touchStartY)\n\n        // 如果水平滑动距离大于垂直滑动距离，阻止默认滚动\n        if (deltaX > deltaY) {\n          event.preventDefault()\n        }\n      }\n    },\n    handleTouchEnd(event) {\n      if (!this.touchStartX || event.changedTouches.length !== 1) return\n\n      const touchEndX = event.changedTouches[0].clientX\n      const touchEndY = event.changedTouches[0].clientY\n      const touchEndTime = Date.now()\n\n      const deltaX = this.touchStartX - touchEndX\n      const deltaY = this.touchStartY - touchEndY\n      const deltaTime = touchEndTime - this.touchStartTime\n\n      // 检查是否为有效的滑动手势\n      const minSwipeDistance = 50 // 最小滑动距离\n      const maxSwipeTime = 500 // 最大滑动时间\n      const maxVerticalDistance = 100 // 最大垂直偏移\n\n      if (Math.abs(deltaX) > minSwipeDistance &&\n          Math.abs(deltaY) < maxVerticalDistance &&\n          deltaTime < maxSwipeTime) {\n\n        if (this.readingMode === 'horizontal') {\n          // 水平滑动模式下的手势处理\n          if (deltaX > 0) {\n            // 向左滑动，下一页\n            this.nextPage()\n          } else {\n            // 向右滑动，上一页\n            this.prevPage()\n          }\n        }\n      } else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {\n        // 点击手势，切换控制栏显示\n        this.toggleControls()\n      }\n\n      // 重置触摸状态\n      this.touchStartX = 0\n      this.touchStartY = 0\n      this.touchStartTime = 0\n    },\n    handleKeydown(event) {\n      if (this.readingMode === 'horizontal') {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault()\n            this.prevPage()\n            break\n          case 'ArrowRight':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case ' ':\n            event.preventDefault()\n            this.nextPage()\n            break\n          case 'Escape':\n            if (this.isFullscreen) {\n              this.exitFullscreen()\n            }\n            break\n        }\n      }\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 添加键盘事件监听\n    document.addEventListener('keydown', this.handleKeydown)\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 监听webkit全屏状态变化（Safari）\n    document.addEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    document.removeEventListener('keydown', this.handleKeydown)\n    document.removeEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n    document.removeEventListener('webkitfullscreenchange', () => {\n      this.isFullscreen = !!document.webkitFullscreenElement\n    })\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .top-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .bottom-controls {\n    height: 55px;\n    padding: 0 12px;\n  }\n\n  .controls-left {\n    gap: 8px;\n    flex: 1;\n    min-width: 0;\n  }\n\n  .controls-right {\n    gap: 8px;\n    flex-shrink: 0;\n  }\n\n  .control-btn {\n    font-size: 14px;\n    padding: 5px 8px;\n  }\n\n  .chapter-info {\n    font-size: 13px;\n    max-width: 120px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .chapter-navigation {\n    gap: 8px;\n    flex: 1;\n    justify-content: center;\n  }\n\n  .chapter-navigation .el-select {\n    width: 140px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .page-info {\n    font-size: 13px;\n    min-width: 60px;\n  }\n\n  .quick-actions .el-button {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  .nav-btn {\n    width: 45px;\n    height: 45px;\n    font-size: 16px;\n  }\n\n  .nav-buttons {\n    padding: 0 15px;\n  }\n\n  .vertical-reader {\n    padding: 10px 5px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 3px;\n    max-width: calc(100% - 10px);\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n    padding: 0 10px;\n    flex-wrap: wrap;\n  }\n\n  .controls-left {\n    gap: 6px;\n    order: 1;\n    width: 100%;\n    justify-content: space-between;\n    margin-bottom: 5px;\n  }\n\n  .controls-right {\n    gap: 6px;\n    order: 2;\n    width: 100%;\n    justify-content: center;\n  }\n\n  .control-btn {\n    font-size: 12px;\n    padding: 4px 6px;\n  }\n\n  .chapter-info {\n    font-size: 12px;\n    max-width: 100px;\n    flex: 1;\n  }\n\n  .reading-mode-toggle {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    flex: 1;\n  }\n\n  .bottom-controls {\n    height: auto;\n    min-height: 50px;\n    flex-direction: column;\n    gap: 8px;\n    padding: 8px 10px;\n  }\n\n  .page-info {\n    order: 1;\n    text-align: center;\n    font-size: 12px;\n    min-width: auto;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    gap: 6px;\n    width: 100%;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .chapter-navigation .el-select {\n    width: 120px !important;\n    margin: 0 5px;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n    min-width: 60px;\n  }\n\n  .quick-actions {\n    order: 3;\n    text-align: center;\n    min-width: auto;\n  }\n\n  .quick-actions .el-button {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n    font-size: 14px;\n  }\n\n  .nav-buttons {\n    padding: 0 10px;\n  }\n\n  .vertical-reader {\n    padding: 5px 3px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n    max-width: calc(100% - 6px);\n  }\n\n  .loading-overlay {\n    font-size: 16px;\n  }\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .top-controls {\n    height: 45px;\n    padding: 0 8px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 4px;\n  }\n\n  .control-btn {\n    font-size: 11px;\n    padding: 3px 5px;\n  }\n\n  .chapter-info {\n    font-size: 11px;\n    max-width: 80px;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 2px 4px;\n    font-size: 9px;\n  }\n\n  .bottom-controls {\n    padding: 6px 8px;\n    gap: 6px;\n  }\n\n  .page-info {\n    font-size: 11px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 100px !important;\n  }\n\n  .chapter-navigation .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n    min-width: 50px;\n  }\n\n  .quick-actions .el-button {\n    padding: 3px 6px;\n    font-size: 10px;\n  }\n\n  .nav-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 12px;\n  }\n\n  .nav-buttons {\n    padding: 0 8px;\n  }\n\n  .vertical-reader {\n    padding: 3px 2px;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 1px;\n    max-width: calc(100% - 4px);\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.7;\n    background-color: rgba(0, 0, 0, 0.8) !important;\n  }\n\n  .nav-btn:active {\n    opacity: 1;\n    transform: scale(0.95);\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n\n  /* 增加触摸区域 */\n  .control-btn,\n  .reading-mode-toggle .el-button,\n  .chapter-navigation .el-button {\n    min-height: 44px; /* iOS推荐的最小触摸目标 */\n  }\n\n  /* 防止双击缩放 */\n  .reading-area {\n    touch-action: pan-y pinch-zoom;\n  }\n\n  .horizontal-reader {\n    touch-action: pan-y;\n  }\n\n  /* 优化滚动性能 */\n  .vertical-reader {\n    -webkit-overflow-scrolling: touch;\n    scroll-behavior: smooth;\n  }\n}\n\n/* 横屏模式优化 */\n@media (max-width: 768px) and (orientation: landscape) {\n  .top-controls,\n  .bottom-controls {\n    height: 45px;\n  }\n\n  .top-controls {\n    padding: 0 15px;\n  }\n\n  .bottom-controls {\n    padding: 0 15px;\n    flex-direction: row;\n    justify-content: space-between;\n  }\n\n  .chapter-navigation {\n    order: 2;\n    flex: 1;\n    max-width: 300px;\n  }\n\n  .page-info {\n    order: 1;\n  }\n\n  .quick-actions {\n    order: 3;\n  }\n\n  .vertical-reader {\n    padding: 5px 10px;\n  }\n}\n</style>\n"], "mappings": ";;;AAuIA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,WAAA;MACAC,gBAAA,EAAAC,QAAA,MAAAC,SAAA;MACAC,YAAA;MACAC,cAAA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;QACAC,EAAA;QACAC,KAAA;QACAC,MAAA,GACA,kGACA,mHACA,kHACA,mHACA,iHACA,mHACA,mHACA;MAEA;MACAC,WAAA,GACA;QAAAH,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAG,QAAA;IACAC,eAAA;MACA,MAAAC,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAV,gBAAA;MACA,OAAAgB,YAAA;IACA;IACAG,eAAA;MACA,MAAAH,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAV,gBAAA;MACA,OAAAgB,YAAA,QAAAH,WAAA,CAAAO,MAAA;IACA;EACA;EACAC,OAAA;IACAC,eAAAC,IAAA;MACA,KAAAzB,WAAA,GAAAyB,IAAA;MACA,IAAAA,IAAA;QACA,KAAAxB,WAAA;MACA;MACA;MACAyB,YAAA,CAAAC,OAAA,gBAAAF,IAAA;IACA;IACAG,eAAA;MACA,KAAAtB,cAAA,SAAAA,cAAA;IACA;IACAuB,iBAAA;MACA,UAAAxB,YAAA;QACA,KAAAyB,eAAA;MACA;QACA,KAAAC,cAAA;MACA;IACA;IACAD,gBAAA;MACA,MAAAE,OAAA,QAAAC,GAAA;MACA,IAAAD,OAAA,CAAAE,iBAAA;QACAF,OAAA,CAAAE,iBAAA;MACA,WAAAF,OAAA,CAAAG,uBAAA;QACAH,OAAA,CAAAG,uBAAA;MACA,WAAAH,OAAA,CAAAI,oBAAA;QACAJ,OAAA,CAAAI,oBAAA;MACA,WAAAJ,OAAA,CAAAK,mBAAA;QACAL,OAAA,CAAAK,mBAAA;MACA;MACA,KAAAhC,YAAA;IACA;IACA0B,eAAA;MACA,IAAAO,QAAA,CAAAP,cAAA;QACAO,QAAA,CAAAP,cAAA;MACA,WAAAO,QAAA,CAAAC,oBAAA;QACAD,QAAA,CAAAC,oBAAA;MACA,WAAAD,QAAA,CAAAE,mBAAA;QACAF,QAAA,CAAAE,mBAAA;MACA,WAAAF,QAAA,CAAAG,gBAAA;QACAH,QAAA,CAAAG,gBAAA;MACA;MACA,KAAApC,YAAA;IACA;IACAqC,SAAA;MACA,SAAAzC,WAAA;QACA,KAAAA,WAAA;QACA,KAAA0C,kBAAA;MACA;IACA;IACAC,SAAA;MACA,SAAA3C,WAAA,QAAAU,cAAA,CAAAG,MAAA,CAAAQ,MAAA;QACA,KAAArB,WAAA;QACA,KAAA0C,kBAAA;MACA;QACA;QACA,SAAAtB,cAAA;UACA,KAAAwB,WAAA;QACA;MACA;IACA;IACAF,mBAAA;MACA,SAAA3C,WAAA;QACA,MAAA8C,SAAA,QAAAC,KAAA,CAAAC,cAAA;QACA,IAAAF,SAAA;UACA,MAAAG,UAAA,GAAAH,SAAA,CAAAI,WAAA;UACAJ,SAAA,CAAAK,UAAA,QAAAlD,WAAA,GAAAgD,UAAA;QACA;MACA;IACA;IACAG,YAAA;MACA,SAAAnC,cAAA;QACA,MAAAC,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAV,gBAAA;QACA,MAAAkD,WAAA,QAAArC,WAAA,CAAAG,YAAA;QACA,KAAAmC,aAAA,CAAAD,WAAA,CAAAxC,EAAA;MACA;IACA;IACAiC,YAAA;MACA,SAAAxB,cAAA;QACA,MAAAH,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAV,gBAAA;QACA,MAAA2C,WAAA,QAAA9B,WAAA,CAAAG,YAAA;QACA,KAAAmC,aAAA,CAAAR,WAAA,CAAAjC,EAAA;MACA;IACA;IACAyC,cAAAjD,SAAA;MACA,KAAAG,OAAA;MACA,KAAAL,gBAAA,GAAAE,SAAA;MACA,KAAAH,WAAA;;MAEA;MACAqD,UAAA;QACA,MAAAC,OAAA,QAAAxC,WAAA,CAAAyC,IAAA,CAAApC,CAAA,IAAAA,CAAA,CAAAR,EAAA,KAAAR,SAAA;QACA,KAAAO,cAAA;UACA,GAAA4C,OAAA;UACAzC,MAAA,GACA,+GAAAV,SAAA,SACA,+GAAAA,SAAA,cACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA;QAEA;QACA,KAAAG,OAAA;;QAEA;QACA,KAAAkD,OAAA,CAAAC,OAAA;UACA7D,IAAA;UACA8D,MAAA;YAAA/C,EAAA,OAAAA,EAAA;YAAAR,SAAA,EAAAA;UAAA;QACA;MACA;IACA;IACAwD,OAAA;MACA,KAAAH,OAAA,CAAAI,IAAA;QAAAhE,IAAA;QAAA8D,MAAA;UAAA/C,EAAA,OAAAA;QAAA;MAAA;IACA;IACAkD,OAAA;MACA,KAAAL,OAAA,CAAAI,IAAA;QAAAhE,IAAA;MAAA;IACA;IACAkE,YAAA;MACA;IAAA,CACA;IACAC,aAAA;MACA;MACAC,OAAA,CAAAC,KAAA;IACA;IACAC,iBAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,OAAA,CAAA/C,MAAA;QACA,KAAAd,WAAA,GAAA4D,KAAA,CAAAC,OAAA,IAAAC,OAAA;QACA,KAAA7D,WAAA,GAAA2D,KAAA,CAAAC,OAAA,IAAAE,OAAA;QACA,KAAA7D,cAAA,GAAA8D,IAAA,CAAAC,GAAA;MACA;IACA;IACAC,gBAAAN,KAAA;MACA;MACA,SAAApE,WAAA,qBAAAoE,KAAA,CAAAC,OAAA,CAAA/C,MAAA;QACA,MAAAqD,MAAA,GAAAP,KAAA,CAAAC,OAAA,IAAAC,OAAA;QACA,MAAAM,MAAA,GAAAR,KAAA,CAAAC,OAAA,IAAAE,OAAA;QACA,MAAAM,MAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAJ,MAAA,QAAAnE,WAAA;QACA,MAAAwE,MAAA,GAAAF,IAAA,CAAAC,GAAA,CAAAH,MAAA,QAAAnE,WAAA;;QAEA;QACA,IAAAoE,MAAA,GAAAG,MAAA;UACAZ,KAAA,CAAAa,cAAA;QACA;MACA;IACA;IACAC,eAAAd,KAAA;MACA,UAAA5D,WAAA,IAAA4D,KAAA,CAAAe,cAAA,CAAA7D,MAAA;MAEA,MAAA8D,SAAA,GAAAhB,KAAA,CAAAe,cAAA,IAAAb,OAAA;MACA,MAAAe,SAAA,GAAAjB,KAAA,CAAAe,cAAA,IAAAZ,OAAA;MACA,MAAAe,YAAA,GAAAd,IAAA,CAAAC,GAAA;MAEA,MAAAI,MAAA,QAAArE,WAAA,GAAA4E,SAAA;MACA,MAAAJ,MAAA,QAAAvE,WAAA,GAAA4E,SAAA;MACA,MAAAE,SAAA,GAAAD,YAAA,QAAA5E,cAAA;;MAEA;MACA,MAAA8E,gBAAA;MACA,MAAAC,YAAA;MACA,MAAAC,mBAAA;;MAEA,IAAAZ,IAAA,CAAAC,GAAA,CAAAF,MAAA,IAAAW,gBAAA,IACAV,IAAA,CAAAC,GAAA,CAAAC,MAAA,IAAAU,mBAAA,IACAH,SAAA,GAAAE,YAAA;QAEA,SAAAzF,WAAA;UACA;UACA,IAAA6E,MAAA;YACA;YACA,KAAAjC,QAAA;UACA;YACA;YACA,KAAAF,QAAA;UACA;QACA;MACA,WAAAoC,IAAA,CAAAC,GAAA,CAAAF,MAAA,UAAAC,IAAA,CAAAC,GAAA,CAAAC,MAAA,UAAAO,SAAA;QACA;QACA,KAAA3D,cAAA;MACA;;MAEA;MACA,KAAApB,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,cAAA;IACA;IACAiF,cAAAvB,KAAA;MACA,SAAApE,WAAA;QACA,QAAAoE,KAAA,CAAAwB,GAAA;UACA;YACAxB,KAAA,CAAAa,cAAA;YACA,KAAAvC,QAAA;YACA;UACA;YACA0B,KAAA,CAAAa,cAAA;YACA,KAAArC,QAAA;YACA;UACA;YACAwB,KAAA,CAAAa,cAAA;YACA,KAAArC,QAAA;YACA;UACA;YACA,SAAAvC,YAAA;cACA,KAAA0B,cAAA;YACA;YACA;QACA;MACA;IACA;EACA;EACA8D,QAAA;IACA;IACA,MAAAC,SAAA,GAAApE,YAAA,CAAAqE,OAAA;IACA,IAAAD,SAAA;MACA,KAAA9F,WAAA,GAAA8F,SAAA;IACA;;IAEA;IACAxD,QAAA,CAAA0D,gBAAA,iBAAAL,aAAA;;IAEA;IACArD,QAAA,CAAA0D,gBAAA;MACA,KAAA3F,YAAA,KAAAiC,QAAA,CAAA2D,iBAAA;IACA;;IAEA;IACA3D,QAAA,CAAA0D,gBAAA;MACA,KAAA3F,YAAA,KAAAiC,QAAA,CAAA4D,uBAAA;IACA;;IAEA;IACA,KAAA7C,aAAA,MAAAnD,gBAAA;EACA;EACAiG,cAAA;IACA;IACA7D,QAAA,CAAA8D,mBAAA,iBAAAT,aAAA;IACArD,QAAA,CAAA8D,mBAAA;MACA,KAAA/F,YAAA,KAAAiC,QAAA,CAAA2D,iBAAA;IACA;IACA3D,QAAA,CAAA8D,mBAAA;MACA,KAAA/F,YAAA,KAAAiC,QAAA,CAAA4D,uBAAA;IACA;EACA;AACA", "ignoreList": []}]}