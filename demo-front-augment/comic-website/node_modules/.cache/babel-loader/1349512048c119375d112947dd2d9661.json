{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicReader.vue", "mtime": 1748420855536}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "data", "readingMode", "currentPage", "currentChapterId", "parseInt", "chapterId", "isFullscreen", "controlsHidden", "loading", "currentChapter", "id", "title", "images", "allChapters", "computed", "hasPrevChapter", "currentIndex", "findIndex", "c", "hasNextChapter", "length", "methods", "setReadingMode", "mode", "localStorage", "setItem", "toggleControls", "toggleFullscreen", "enterFullscreen", "exitFullscreen", "element", "$el", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "document", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "prevPage", "updateImageDisplay", "nextPage", "nextChapter", "container", "$refs", "imageContainer", "imageWidth", "clientWidth", "scrollLeft", "prevChapter", "changeChapter", "setTimeout", "chapter", "find", "$router", "replace", "params", "goBack", "push", "goHome", "onImageLoad", "onImageError", "console", "error", "mounted", "savedMode", "getItem", "addEventListener", "fullscreenElement"], "sources": ["src/views/ComicReader.vue"], "sourcesContent": ["<template>\n  <div class=\"comic-reader\" :class=\"{ 'fullscreen': isFullscreen }\">\n    <!-- 顶部控制栏 -->\n    <div class=\"top-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"controls-left\">\n        <el-button @click=\"goBack\" icon=\"el-icon-arrow-left\" type=\"text\" class=\"control-btn\">\n          返回\n        </el-button>\n        <span class=\"chapter-info\">{{ currentChapter.title }}</span>\n      </div>\n      <div class=\"controls-right\">\n        <el-button-group class=\"reading-mode-toggle\">\n          <el-button\n            :type=\"readingMode === 'horizontal' ? 'primary' : ''\"\n            @click=\"setReadingMode('horizontal')\"\n            size=\"small\"\n          >\n            左右滑动\n          </el-button>\n          <el-button\n            :type=\"readingMode === 'vertical' ? 'primary' : ''\"\n            @click=\"setReadingMode('vertical')\"\n            size=\"small\"\n          >\n            上下拼接\n          </el-button>\n        </el-button-group>\n        <el-button @click=\"toggleFullscreen\" :icon=\"isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'\" type=\"text\" class=\"control-btn\">\n          {{ isFullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 阅读区域 -->\n    <div class=\"reading-area\" @click=\"toggleControls\">\n      <!-- 水平滑动模式 -->\n      <div v-if=\"readingMode === 'horizontal'\" class=\"horizontal-reader\">\n        <div class=\"image-container\" ref=\"imageContainer\">\n          <img\n            v-for=\"(image, index) in currentChapter.images\"\n            :key=\"index\"\n            :src=\"image\"\n            :alt=\"`第${currentPage + 1}页`\"\n            class=\"comic-image\"\n            :class=\"{ 'active': index === currentPage }\"\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </div>\n\n        <!-- 左右导航按钮 -->\n        <div class=\"nav-buttons\">\n          <el-button\n            v-if=\"currentPage > 0\"\n            @click.stop=\"prevPage\"\n            class=\"nav-btn nav-btn-left\"\n            icon=\"el-icon-arrow-left\"\n            circle\n          />\n          <el-button\n            v-if=\"currentPage < currentChapter.images.length - 1\"\n            @click.stop=\"nextPage\"\n            class=\"nav-btn nav-btn-right\"\n            icon=\"el-icon-arrow-right\"\n            circle\n          />\n        </div>\n      </div>\n\n      <!-- 垂直拼接模式 -->\n      <div v-else class=\"vertical-reader\" ref=\"verticalReader\">\n        <img\n          v-for=\"(image, index) in currentChapter.images\"\n          :key=\"index\"\n          :src=\"image\"\n          :alt=\"`第${index + 1}页`\"\n          class=\"comic-image-vertical\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n      </div>\n    </div>\n\n    <!-- 底部导航栏 -->\n    <div class=\"bottom-controls\" :class=\"{ 'hidden': controlsHidden }\">\n      <div class=\"page-info\" v-if=\"readingMode === 'horizontal'\">\n        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>\n      </div>\n\n      <div class=\"chapter-navigation\">\n        <el-button\n          @click=\"prevChapter\"\n          :disabled=\"!hasPrevChapter\"\n          size=\"small\"\n        >\n          上一章\n        </el-button>\n\n        <el-select v-model=\"currentChapterId\" @change=\"changeChapter\" size=\"small\" style=\"width: 200px;\">\n          <el-option\n            v-for=\"chapter in allChapters\"\n            :key=\"chapter.id\"\n            :label=\"chapter.title\"\n            :value=\"chapter.id\"\n          />\n        </el-select>\n\n        <el-button\n          @click=\"nextChapter\"\n          :disabled=\"!hasNextChapter\"\n          size=\"small\"\n        >\n          下一章\n        </el-button>\n      </div>\n\n      <div class=\"quick-actions\">\n        <el-button @click=\"goHome\" size=\"small\">回到首页</el-button>\n      </div>\n    </div>\n\n    <!-- 加载提示 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-loading-text>加载中...</el-loading-text>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicReader',\n  props: ['id', 'chapterId'],\n  data() {\n    return {\n      readingMode: 'vertical', // 默认垂直拼接模式\n      currentPage: 0,\n      currentChapterId: parseInt(this.chapterId),\n      isFullscreen: false,\n      controlsHidden: false,\n      loading: false,\n      currentChapter: {\n        id: 1,\n        title: '第1话 致两千年后的你',\n        images: [\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',\n          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'\n        ]\n      },\n      allChapters: [\n        { id: 1, title: '第1话 致两千年后的你' },\n        { id: 2, title: '第2话 那一天' },\n        { id: 3, title: '第3话 解散式之夜' },\n        { id: 4, title: '第4话 初阵' },\n        { id: 5, title: '第5话 心脏的跳动声' }\n      ]\n    }\n  },\n  computed: {\n    hasPrevChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex > 0\n    },\n    hasNextChapter() {\n      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n      return currentIndex < this.allChapters.length - 1\n    }\n  },\n  methods: {\n    setReadingMode(mode) {\n      this.readingMode = mode\n      if (mode === 'horizontal') {\n        this.currentPage = 0\n      }\n      // 保存用户偏好到localStorage\n      localStorage.setItem('readingMode', mode)\n    },\n    toggleControls() {\n      this.controlsHidden = !this.controlsHidden\n    },\n    toggleFullscreen() {\n      if (!this.isFullscreen) {\n        this.enterFullscreen()\n      } else {\n        this.exitFullscreen()\n      }\n    },\n    enterFullscreen() {\n      const element = this.$el\n      if (element.requestFullscreen) {\n        element.requestFullscreen()\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen()\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen()\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen()\n      }\n      this.isFullscreen = true\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen()\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen()\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen()\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen()\n      }\n      this.isFullscreen = false\n    },\n    prevPage() {\n      if (this.currentPage > 0) {\n        this.currentPage--\n        this.updateImageDisplay()\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.currentChapter.images.length - 1) {\n        this.currentPage++\n        this.updateImageDisplay()\n      } else {\n        // 自动跳转到下一章\n        if (this.hasNextChapter) {\n          this.nextChapter()\n        }\n      }\n    },\n    updateImageDisplay() {\n      if (this.readingMode === 'horizontal') {\n        const container = this.$refs.imageContainer\n        if (container) {\n          const imageWidth = container.clientWidth\n          container.scrollLeft = this.currentPage * imageWidth\n        }\n      }\n    },\n    prevChapter() {\n      if (this.hasPrevChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const prevChapter = this.allChapters[currentIndex - 1]\n        this.changeChapter(prevChapter.id)\n      }\n    },\n    nextChapter() {\n      if (this.hasNextChapter) {\n        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)\n        const nextChapter = this.allChapters[currentIndex + 1]\n        this.changeChapter(nextChapter.id)\n      }\n    },\n    changeChapter(chapterId) {\n      this.loading = true\n      this.currentChapterId = chapterId\n      this.currentPage = 0\n\n      // 模拟加载章节数据\n      setTimeout(() => {\n        const chapter = this.allChapters.find(c => c.id === chapterId)\n        this.currentChapter = {\n          ...chapter,\n          images: [\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,\n            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`\n          ]\n        }\n        this.loading = false\n\n        // 更新URL\n        this.$router.replace({\n          name: 'ComicReader',\n          params: { id: this.id, chapterId: chapterId }\n        })\n      }, 1000)\n    },\n    goBack() {\n      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })\n    },\n    goHome() {\n      this.$router.push({ name: 'ComicList' })\n    },\n    onImageLoad() {\n      // 图片加载完成\n    },\n    onImageError() {\n      // 图片加载失败\n      console.error('图片加载失败')\n    }\n  },\n  mounted() {\n    // 从localStorage读取用户偏好的阅读模式\n    const savedMode = localStorage.getItem('readingMode')\n    if (savedMode) {\n      this.readingMode = savedMode\n    }\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', () => {\n      this.isFullscreen = !!document.fullscreenElement\n    })\n\n    // 加载当前章节数据\n    this.changeChapter(this.currentChapterId)\n  }\n}\n</script>\n\n<style scoped>\n.comic-reader {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n  overflow: hidden;\n}\n\n.comic-reader.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n/* 顶部控制栏 */\n.top-controls {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.top-controls.hidden {\n  transform: translateY(-100%);\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  color: white !important;\n  font-size: 16px;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.chapter-info {\n  color: white;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.reading-mode-toggle {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n/* 阅读区域 */\n.reading-area {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* 水平滑动模式 */\n.horizontal-reader {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.image-container {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow-x: auto;\n  scroll-behavior: smooth;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.image-container::-webkit-scrollbar {\n  display: none;\n}\n\n.comic-image {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  background-color: #000;\n  display: none;\n}\n\n.comic-image.active {\n  display: block;\n}\n\n/* 导航按钮 */\n.nav-buttons {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  pointer-events: all;\n  background-color: rgba(0, 0, 0, 0.6) !important;\n  border: none !important;\n  color: white !important;\n  width: 50px;\n  height: 50px;\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.nav-btn:hover {\n  opacity: 1;\n  background-color: rgba(0, 0, 0, 0.8) !important;\n}\n\n/* 垂直拼接模式 */\n.vertical-reader {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.comic-image-vertical {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n  background-color: #000;\n}\n\n/* 底部控制栏 */\n.bottom-controls {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  z-index: 1000;\n  transition: transform 0.3s ease;\n}\n\n.bottom-controls.hidden {\n  transform: translateY(100%);\n}\n\n.page-info {\n  color: white;\n  font-size: 14px;\n  min-width: 80px;\n}\n\n.chapter-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.quick-actions {\n  min-width: 80px;\n  text-align: right;\n}\n\n/* 加载提示 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  color: white;\n  font-size: 18px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .top-controls,\n  .bottom-controls {\n    padding: 0 10px;\n  }\n\n  .controls-left,\n  .controls-right {\n    gap: 10px;\n  }\n\n  .chapter-info {\n    font-size: 14px;\n    max-width: 150px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .chapter-navigation {\n    gap: 10px;\n  }\n\n  .chapter-navigation .el-select {\n    width: 150px !important;\n  }\n\n  .nav-btn {\n    width: 40px;\n    height: 40px;\n  }\n\n  .vertical-reader {\n    padding: 10px 0;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 2px;\n  }\n}\n\n@media (max-width: 480px) {\n  .top-controls {\n    height: 50px;\n  }\n\n  .bottom-controls {\n    height: 50px;\n    flex-direction: column;\n    gap: 5px;\n    padding: 5px 10px;\n  }\n\n  .chapter-navigation {\n    order: 1;\n  }\n\n  .page-info,\n  .quick-actions {\n    order: 2;\n    min-width: auto;\n  }\n\n  .reading-mode-toggle .el-button {\n    padding: 5px 8px;\n    font-size: 12px;\n  }\n}\n\n/* 触摸设备优化 */\n@media (hover: none) and (pointer: coarse) {\n  .nav-btn {\n    opacity: 0.5;\n  }\n\n  .comic-image-vertical {\n    margin-bottom: 0;\n  }\n}\n</style>\n"], "mappings": ";;;AAiIA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,WAAA;MACAC,gBAAA,EAAAC,QAAA,MAAAC,SAAA;MACAC,YAAA;MACAC,cAAA;MACAC,OAAA;MACAC,cAAA;QACAC,EAAA;QACAC,KAAA;QACAC,MAAA,GACA,kGACA,mHACA,kHACA,mHACA,iHACA,mHACA,mHACA;MAEA;MACAC,WAAA,GACA;QAAAH,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAG,QAAA;IACAC,eAAA;MACA,MAAAC,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAP,gBAAA;MACA,OAAAa,YAAA;IACA;IACAG,eAAA;MACA,MAAAH,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAP,gBAAA;MACA,OAAAa,YAAA,QAAAH,WAAA,CAAAO,MAAA;IACA;EACA;EACAC,OAAA;IACAC,eAAAC,IAAA;MACA,KAAAtB,WAAA,GAAAsB,IAAA;MACA,IAAAA,IAAA;QACA,KAAArB,WAAA;MACA;MACA;MACAsB,YAAA,CAAAC,OAAA,gBAAAF,IAAA;IACA;IACAG,eAAA;MACA,KAAAnB,cAAA,SAAAA,cAAA;IACA;IACAoB,iBAAA;MACA,UAAArB,YAAA;QACA,KAAAsB,eAAA;MACA;QACA,KAAAC,cAAA;MACA;IACA;IACAD,gBAAA;MACA,MAAAE,OAAA,QAAAC,GAAA;MACA,IAAAD,OAAA,CAAAE,iBAAA;QACAF,OAAA,CAAAE,iBAAA;MACA,WAAAF,OAAA,CAAAG,uBAAA;QACAH,OAAA,CAAAG,uBAAA;MACA,WAAAH,OAAA,CAAAI,oBAAA;QACAJ,OAAA,CAAAI,oBAAA;MACA,WAAAJ,OAAA,CAAAK,mBAAA;QACAL,OAAA,CAAAK,mBAAA;MACA;MACA,KAAA7B,YAAA;IACA;IACAuB,eAAA;MACA,IAAAO,QAAA,CAAAP,cAAA;QACAO,QAAA,CAAAP,cAAA;MACA,WAAAO,QAAA,CAAAC,oBAAA;QACAD,QAAA,CAAAC,oBAAA;MACA,WAAAD,QAAA,CAAAE,mBAAA;QACAF,QAAA,CAAAE,mBAAA;MACA,WAAAF,QAAA,CAAAG,gBAAA;QACAH,QAAA,CAAAG,gBAAA;MACA;MACA,KAAAjC,YAAA;IACA;IACAkC,SAAA;MACA,SAAAtC,WAAA;QACA,KAAAA,WAAA;QACA,KAAAuC,kBAAA;MACA;IACA;IACAC,SAAA;MACA,SAAAxC,WAAA,QAAAO,cAAA,CAAAG,MAAA,CAAAQ,MAAA;QACA,KAAAlB,WAAA;QACA,KAAAuC,kBAAA;MACA;QACA;QACA,SAAAtB,cAAA;UACA,KAAAwB,WAAA;QACA;MACA;IACA;IACAF,mBAAA;MACA,SAAAxC,WAAA;QACA,MAAA2C,SAAA,QAAAC,KAAA,CAAAC,cAAA;QACA,IAAAF,SAAA;UACA,MAAAG,UAAA,GAAAH,SAAA,CAAAI,WAAA;UACAJ,SAAA,CAAAK,UAAA,QAAA/C,WAAA,GAAA6C,UAAA;QACA;MACA;IACA;IACAG,YAAA;MACA,SAAAnC,cAAA;QACA,MAAAC,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAP,gBAAA;QACA,MAAA+C,WAAA,QAAArC,WAAA,CAAAG,YAAA;QACA,KAAAmC,aAAA,CAAAD,WAAA,CAAAxC,EAAA;MACA;IACA;IACAiC,YAAA;MACA,SAAAxB,cAAA;QACA,MAAAH,YAAA,QAAAH,WAAA,CAAAI,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,UAAAP,gBAAA;QACA,MAAAwC,WAAA,QAAA9B,WAAA,CAAAG,YAAA;QACA,KAAAmC,aAAA,CAAAR,WAAA,CAAAjC,EAAA;MACA;IACA;IACAyC,cAAA9C,SAAA;MACA,KAAAG,OAAA;MACA,KAAAL,gBAAA,GAAAE,SAAA;MACA,KAAAH,WAAA;;MAEA;MACAkD,UAAA;QACA,MAAAC,OAAA,QAAAxC,WAAA,CAAAyC,IAAA,CAAApC,CAAA,IAAAA,CAAA,CAAAR,EAAA,KAAAL,SAAA;QACA,KAAAI,cAAA;UACA,GAAA4C,OAAA;UACAzC,MAAA,GACA,+GAAAP,SAAA,SACA,+GAAAA,SAAA,cACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA,eACA,+GAAAA,SAAA;QAEA;QACA,KAAAG,OAAA;;QAEA;QACA,KAAA+C,OAAA,CAAAC,OAAA;UACA1D,IAAA;UACA2D,MAAA;YAAA/C,EAAA,OAAAA,EAAA;YAAAL,SAAA,EAAAA;UAAA;QACA;MACA;IACA;IACAqD,OAAA;MACA,KAAAH,OAAA,CAAAI,IAAA;QAAA7D,IAAA;QAAA2D,MAAA;UAAA/C,EAAA,OAAAA;QAAA;MAAA;IACA;IACAkD,OAAA;MACA,KAAAL,OAAA,CAAAI,IAAA;QAAA7D,IAAA;MAAA;IACA;IACA+D,YAAA;MACA;IAAA,CACA;IACAC,aAAA;MACA;MACAC,OAAA,CAAAC,KAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,SAAA,GAAA1C,YAAA,CAAA2C,OAAA;IACA,IAAAD,SAAA;MACA,KAAAjE,WAAA,GAAAiE,SAAA;IACA;;IAEA;IACA9B,QAAA,CAAAgC,gBAAA;MACA,KAAA9D,YAAA,KAAA8B,QAAA,CAAAiC,iBAAA;IACA;;IAEA;IACA,KAAAlB,aAAA,MAAAhD,gBAAA;EACA;AACA", "ignoreList": []}]}