{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748422167106}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/thread-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmlsdGVyLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDb21pY0xpc3QnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzZWFyY2hLZXl3b3JkOiAnJywKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAxMiwKICAgICAgY29taWNzOiBbewogICAgICAgIGlkOiAxLAogICAgICAgIHRpdGxlOiAn6L+b5Ye755qE5beo5Lq6JywKICAgICAgICBjb3ZlcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTc4NjYyOTk2NDQyLTQ4ZjYwMTAzZmM5Nj93PTIwMCZoPTI4MCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcicsCiAgICAgICAgbGF0ZXN0Q2hhcHRlcjogJ+esrDEzOeivnScsCiAgICAgICAgdXBkYXRlVGltZTogJzIwMjQtMDEtMTUnCiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICB0aXRsZTogJ+msvOeBreS5i+WIgycsCiAgICAgICAgY292ZXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz0yMDAmaD0yODAmZml0PWNyb3AmY3JvcD1jZW50ZXImc2F0PS0xMDAmaHVlPTI0MCcsCiAgICAgICAgbGF0ZXN0Q2hhcHRlcjogJ+esrDIwNeivnScsCiAgICAgICAgdXBkYXRlVGltZTogJzIwMjQtMDEtMTQnCiAgICAgIH0sIHsKICAgICAgICBpZDogMywKICAgICAgICB0aXRsZTogJ+a1t+i0vOeOiycsCiAgICAgICAgY292ZXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz0yMDAmaD0yODAmZml0PWNyb3AmY3JvcD1jZW50ZXImc2F0PS0xMDAmaHVlPTMwJywKICAgICAgICBsYXRlc3RDaGFwdGVyOiAn56ysMTEwMOivnScsCiAgICAgICAgdXBkYXRlVGltZTogJzIwMjQtMDEtMTMnCiAgICAgIH0sIHsKICAgICAgICBpZDogNCwKICAgICAgICB0aXRsZTogJ+eBq+W9seW/jeiAhScsCiAgICAgICAgY292ZXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz0yMDAmaD0yODAmZml0PWNyb3AmY3JvcD1jZW50ZXImc2F0PS0xMDAmaHVlPTI3MCcsCiAgICAgICAgbGF0ZXN0Q2hhcHRlcjogJ+esrDcwMOivnScsCiAgICAgICAgdXBkYXRlVGltZTogJzIwMjQtMDEtMTInCiAgICAgIH0sIHsKICAgICAgICBpZDogNSwKICAgICAgICB0aXRsZTogJ+m+meePoCcsCiAgICAgICAgY292ZXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz0yMDAmaD0yODAmZml0PWNyb3AmY3JvcD1jZW50ZXImc2F0PS0xMDAmaHVlPTAnLAogICAgICAgIGxhdGVzdENoYXB0ZXI6ICfnrKw1MTnor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTExJwogICAgICB9LCB7CiAgICAgICAgaWQ6IDYsCiAgICAgICAgdGl0bGU6ICfmrbvnpZ4nLAogICAgICAgIGNvdmVyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Nzg2NjI5OTY0NDItNDhmNjAxMDNmYzk2P3c9MjAwJmg9MjgwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJnNhdD0tMTAwJmh1ZT0xODAnLAogICAgICAgIGxhdGVzdENoYXB0ZXI6ICfnrKw2ODbor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTEwJwogICAgICB9LCB7CiAgICAgICAgaWQ6IDcsCiAgICAgICAgdGl0bGU6ICfmiJHnmoToi7Hpm4TlrabpmaInLAogICAgICAgIGNvdmVyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Nzg2NjI5OTY0NDItNDhmNjAxMDNmYzk2P3c9MjAwJmg9MjgwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJnNhdD0tMTAwJmh1ZT0xMjAnLAogICAgICAgIGxhdGVzdENoYXB0ZXI6ICfnrKwzOTDor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTA5JwogICAgICB9LCB7CiAgICAgICAgaWQ6IDgsCiAgICAgICAgdGl0bGU6ICfkuJzkuqzllrDnp40nLAogICAgICAgIGNvdmVyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Nzg2NjI5OTY0NDItNDhmNjAxMDNmYzk2P3c9MjAwJmg9MjgwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJnNhdD0tMTAwJmh1ZT0zMDAnLAogICAgICAgIGxhdGVzdENoYXB0ZXI6ICfnrKwxNznor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTA4JwogICAgICB9LCB7CiAgICAgICAgaWQ6IDksCiAgICAgICAgdGl0bGU6ICfnuqblrprnmoTmoqblubvlspsnLAogICAgICAgIGNvdmVyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Nzg2NjI5OTY0NDItNDhmNjAxMDNmYzk2P3c9MjAwJmg9MjgwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJnNhdD0tMTAwJmh1ZT02MCcsCiAgICAgICAgbGF0ZXN0Q2hhcHRlcjogJ+esrDE4MeivnScsCiAgICAgICAgdXBkYXRlVGltZTogJzIwMjQtMDEtMDcnCiAgICAgIH0sIHsKICAgICAgICBpZDogMTAsCiAgICAgICAgdGl0bGU6ICflkpLmnK/lm57miJgnLAogICAgICAgIGNvdmVyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Nzg2NjI5OTY0NDItNDhmNjAxMDNmYzk2P3c9MjAwJmg9MjgwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJnNhdD0tMTAwJmh1ZT0yMTAnLAogICAgICAgIGxhdGVzdENoYXB0ZXI6ICfnrKwyNDXor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTA2JwogICAgICB9LCB7CiAgICAgICAgaWQ6IDExLAogICAgICAgIHRpdGxlOiAn6ZO+6ZSv5Lq6JywKICAgICAgICBjb3ZlcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTc4NjYyOTk2NDQyLTQ4ZjYwMTAzZmM5Nj93PTIwMCZoPTI4MCZmaXQ9Y3JvcCZjcm9wPWNlbnRlciZzYXQ9LTEwMCZodWU9MzMwJywKICAgICAgICBsYXRlc3RDaGFwdGVyOiAn56ysMTUw6K+dJywKICAgICAgICB1cGRhdGVUaW1lOiAnMjAyNC0wMS0wNScKICAgICAgfSwgewogICAgICAgIGlkOiAxMiwKICAgICAgICB0aXRsZTogJ+mXtOiwjei/h+WutuWuticsCiAgICAgICAgY292ZXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz0yMDAmaD0yODAmZml0PWNyb3AmY3JvcD1jZW50ZXImc2F0PS0xMDAmaHVlPTkwJywKICAgICAgICBsYXRlc3RDaGFwdGVyOiAn56ysOTXor50nLAogICAgICAgIHVwZGF0ZVRpbWU6ICcyMDI0LTAxLTA0JwogICAgICB9XQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBmaWx0ZXJlZENvbWljcygpIHsKICAgICAgbGV0IGZpbHRlcmVkID0gdGhpcy5jb21pY3M7CiAgICAgIGlmICh0aGlzLnNlYXJjaEtleXdvcmQpIHsKICAgICAgICBmaWx0ZXJlZCA9IHRoaXMuY29taWNzLmZpbHRlcihjb21pYyA9PiBjb21pYy50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHRoaXMuc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpKSk7CiAgICAgIH0KICAgICAgY29uc3Qgc3RhcnQgPSAodGhpcy5jdXJyZW50UGFnZSAtIDEpICogdGhpcy5wYWdlU2l6ZTsKICAgICAgY29uc3QgZW5kID0gc3RhcnQgKyB0aGlzLnBhZ2VTaXplOwogICAgICByZXR1cm4gZmlsdGVyZWQuc2xpY2Uoc3RhcnQsIGVuZCk7CiAgICB9LAogICAgdG90YWxGaWx0ZXJlZENvbWljcygpIHsKICAgICAgbGV0IGZpbHRlcmVkID0gdGhpcy5jb21pY3M7CiAgICAgIGlmICh0aGlzLnNlYXJjaEtleXdvcmQpIHsKICAgICAgICBmaWx0ZXJlZCA9IHRoaXMuY29taWNzLmZpbHRlcihjb21pYyA9PiBjb21pYy50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHRoaXMuc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpKSk7CiAgICAgIH0KICAgICAgcmV0dXJuIGZpbHRlcmVkLmxlbmd0aDsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIHNlYXJjaENvbWljcygpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICB9LAogICAgZ29Ub0RldGFpbChjb21pY0lkKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAnQ29taWNEZXRhaWwnLAogICAgICAgIHBhcmFtczogewogICAgICAgICAgaWQ6IGNvbWljSWQKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZVNpemUgPSB2YWw7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWw7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "searchKeyword", "currentPage", "pageSize", "comics", "id", "title", "cover", "latestChapter", "updateTime", "computed", "filteredComics", "filtered", "filter", "comic", "toLowerCase", "includes", "start", "end", "slice", "totalFilteredComics", "length", "methods", "searchComics", "goToDetail", "comicId", "$router", "push", "params", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/ComicList.vue"], "sourcesContent": ["<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div\n            v-for=\"comic in filteredComics\"\n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalFilteredComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        },\n        {\n          id: 7,\n          title: '我的英雄学院',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',\n          latestChapter: '第390话',\n          updateTime: '2024-01-09'\n        },\n        {\n          id: 8,\n          title: '东京喰种',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',\n          latestChapter: '第179话',\n          updateTime: '2024-01-08'\n        },\n        {\n          id: 9,\n          title: '约定的梦幻岛',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',\n          latestChapter: '第181话',\n          updateTime: '2024-01-07'\n        },\n        {\n          id: 10,\n          title: '咒术回战',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',\n          latestChapter: '第245话',\n          updateTime: '2024-01-06'\n        },\n        {\n          id: 11,\n          title: '链锯人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',\n          latestChapter: '第150话',\n          updateTime: '2024-01-05'\n        },\n        {\n          id: 12,\n          title: '间谍过家家',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',\n          latestChapter: '第95话',\n          updateTime: '2024-01-04'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    },\n    totalFilteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      return filtered.length\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 10;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n  position: relative;\n  z-index: 10;\n}\n\n.search-box .el-input {\n  flex: 1;\n  position: relative;\n  z-index: 11;\n}\n\n.search-box .el-button {\n  position: relative;\n  z-index: 12;\n  flex-shrink: 0;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式优化 */\n@media (max-width: 768px) {\n  .header {\n    height: auto;\n    min-height: 70px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 15px;\n    gap: 15px;\n  }\n\n  .logo h2 {\n    font-size: 20px;\n    text-align: center;\n  }\n\n  .search-box {\n    margin: 0;\n    max-width: 100%;\n    width: 100%;\n    position: relative;\n    z-index: 20;\n    background: transparent;\n  }\n\n  .search-box .el-input {\n    font-size: 16px; /* 防止iOS缩放 */\n    position: relative;\n    z-index: 21;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: white !important;\n    border: 1px solid rgba(255, 255, 255, 0.5) !important;\n    color: #333 !important;\n    position: relative !important;\n    z-index: 1000 !important;\n  }\n\n  .search-box .el-input .el-input__inner::placeholder {\n    color: #999 !important;\n  }\n\n  .search-box .el-button {\n    position: relative !important;\n    z-index: 1001 !important;\n    background-color: rgba(255, 255, 255, 0.9) !important;\n    border-color: rgba(255, 255, 255, 0.9) !important;\n    color: #667eea !important;\n  }\n\n  .search-box .el-button:hover {\n    background-color: white !important;\n    border-color: white !important;\n    color: #667eea !important;\n  }\n\n  .user-actions {\n    justify-content: center;\n    width: 100%;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n    gap: 12px;\n  }\n\n  .comic-cover {\n    height: 200px;\n  }\n\n  .comic-info {\n    padding: 12px;\n  }\n\n  .comic-title {\n    font-size: 14px;\n    line-height: 1.3;\n  }\n\n  .comic-latest {\n    font-size: 13px;\n  }\n\n  .comic-update {\n    font-size: 11px;\n  }\n\n  .pagination-wrapper {\n    margin-top: 30px;\n  }\n\n  .pagination-wrapper .el-pagination {\n    text-align: center;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager li {\n    min-width: 32px;\n    height: 32px;\n    line-height: 32px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    padding: 12px;\n    gap: 12px;\n    position: relative;\n    z-index: 100;\n  }\n\n  .logo h2 {\n    font-size: 18px;\n  }\n\n  .search-box {\n    order: 2;\n    width: 100%;\n    margin: 0;\n    position: relative;\n    z-index: 101;\n  }\n\n  .search-box .el-input {\n    position: relative;\n    z-index: 102;\n  }\n\n  .search-box .el-input .el-input__inner {\n    background-color: white;\n    border: 1px solid #dcdfe6;\n    color: #333;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .search-box .el-button {\n    position: relative;\n    z-index: 103;\n    background-color: #409eff;\n    border-color: #409eff;\n    color: white;\n    height: 40px;\n    min-width: 60px;\n  }\n\n  .user-actions {\n    order: 3;\n    width: 100%;\n    justify-content: center;\n    position: relative;\n    z-index: 100;\n  }\n\n  .main-content {\n    padding: 12px;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 10px;\n  }\n\n  .comic-cover {\n    height: 180px;\n  }\n\n  .comic-info {\n    padding: 10px;\n  }\n\n  .comic-title {\n    font-size: 13px;\n    margin-bottom: 6px;\n  }\n\n  .comic-latest {\n    font-size: 12px;\n    margin-bottom: 3px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n\n  .user-actions .el-button {\n    padding: 8px 15px;\n    font-size: 14px;\n  }\n\n  .search-box .el-button {\n    padding: 10px 15px;\n  }\n\n  /* 分页组件移动端优化 */\n  .pagination-wrapper .el-pagination {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__sizes {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__total {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .el-pager {\n    margin: 0 5px 10px 0;\n  }\n\n  .pagination-wrapper .el-pagination .btn-prev,\n  .pagination-wrapper .el-pagination .btn-next {\n    margin: 0 2px 10px 2px;\n  }\n\n  .pagination-wrapper .el-pagination .el-pagination__jump {\n    margin: 0 5px 10px 0;\n  }\n}\n\n/* 修复搜索框遮挡问题 */\n.search-box .el-input__inner {\n  position: relative !important;\n  z-index: 1000 !important;\n  background-color: white !important;\n}\n\n.search-box .el-button {\n  position: relative !important;\n  z-index: 1001 !important;\n}\n\n/* Element UI 下拉框层级修复 */\n.el-select-dropdown {\n  z-index: 999 !important;\n}\n\n.el-popper {\n  z-index: 999 !important;\n}\n\n/* 超小屏幕优化 */\n@media (max-width: 360px) {\n  .header-content {\n    padding: 10px;\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n\n  .comic-grid {\n    gap: 8px;\n  }\n\n  .comic-cover {\n    height: 160px;\n  }\n\n  .comic-info {\n    padding: 8px;\n  }\n\n  .comic-title {\n    font-size: 12px;\n  }\n\n  .comic-latest {\n    font-size: 11px;\n  }\n\n  .comic-update {\n    font-size: 10px;\n  }\n}\n</style>\n"], "mappings": ";;;AAoEA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,MAAA,GACA;QACAC,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,eAAA;MACA,IAAAC,QAAA,QAAAR,MAAA;MACA,SAAAH,aAAA;QACAW,QAAA,QAAAR,MAAA,CAAAS,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAR,KAAA,CAAAS,WAAA,GAAAC,QAAA,MAAAf,aAAA,CAAAc,WAAA,GACA;MACA;MACA,MAAAE,KAAA,SAAAf,WAAA,aAAAC,QAAA;MACA,MAAAe,GAAA,GAAAD,KAAA,QAAAd,QAAA;MACA,OAAAS,QAAA,CAAAO,KAAA,CAAAF,KAAA,EAAAC,GAAA;IACA;IACAE,oBAAA;MACA,IAAAR,QAAA,QAAAR,MAAA;MACA,SAAAH,aAAA;QACAW,QAAA,QAAAR,MAAA,CAAAS,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAR,KAAA,CAAAS,WAAA,GAAAC,QAAA,MAAAf,aAAA,CAAAc,WAAA,GACA;MACA;MACA,OAAAH,QAAA,CAAAS,MAAA;IACA;EACA;EACAC,OAAA;IACAC,aAAA;MACA,KAAArB,WAAA;IACA;IACAsB,WAAAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAA5B,IAAA;QAAA6B,MAAA;UAAAvB,EAAA,EAAAoB;QAAA;MAAA;IACA;IACAI,iBAAAC,GAAA;MACA,KAAA3B,QAAA,GAAA2B,GAAA;MACA,KAAA5B,WAAA;IACA;IACA6B,oBAAAD,GAAA;MACA,KAAA5B,WAAA,GAAA4B,GAAA;IACA;EACA;AACA", "ignoreList": []}]}