{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=template&id=686e526f&scoped=true", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748422167106}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1748420070797}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "on", "keyup", "$event", "type", "indexOf", "_k", "keyCode", "key", "searchComics", "apply", "arguments", "model", "value", "searchKeyword", "callback", "$$v", "expression", "_l", "filteredComics", "comic", "id", "click", "goToDetail", "cover", "title", "_s", "latestChapter", "updateTime", "currentPage", "pageSize", "totalFilteredComics", "handleSizeChange", "handleCurrentChange", "staticRenderFns"], "sources": ["/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"comic-list\"},[_c('el-header',{staticClass:\"header\"},[_c('div',{staticClass:\"header-content\"},[_c('div',{staticClass:\"logo\"},[_c('h2',[_vm._v(\"漫画网站\")])]),_c('div',{staticClass:\"search-box\"},[_c('el-input',{attrs:{\"placeholder\":\"搜索漫画...\",\"prefix-icon\":\"el-icon-search\",\"clearable\":\"\"},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchComics.apply(null, arguments)}},model:{value:(_vm.searchKeyword),callback:function ($$v) {_vm.searchKeyword=$$v},expression:\"searchKeyword\"}}),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchComics}},[_vm._v(\"搜索\")])],1),_c('div',{staticClass:\"user-actions\"},[_c('el-button',{attrs:{\"type\":\"text\"}},[_vm._v(\"登录\")]),_c('el-button',{attrs:{\"type\":\"primary\"}},[_vm._v(\"注册\")])],1)])]),_c('el-main',{staticClass:\"main-content\"},[_c('div',{staticClass:\"content-wrapper\"},[_c('div',{staticClass:\"comic-grid\"},_vm._l((_vm.filteredComics),function(comic){return _c('div',{key:comic.id,staticClass:\"comic-card\",on:{\"click\":function($event){return _vm.goToDetail(comic.id)}}},[_c('div',{staticClass:\"comic-cover\"},[_c('img',{attrs:{\"src\":comic.cover,\"alt\":comic.title}}),_c('div',{staticClass:\"comic-overlay\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\"查看详情\")])],1)]),_c('div',{staticClass:\"comic-info\"},[_c('h3',{staticClass:\"comic-title\"},[_vm._v(_vm._s(comic.title))]),_c('p',{staticClass:\"comic-latest\"},[_vm._v(\"最新: \"+_vm._s(comic.latestChapter))]),_c('p',{staticClass:\"comic-update\"},[_vm._v(_vm._s(comic.updateTime))])])])}),0),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.currentPage,\"page-sizes\":[12, 24, 36, 48],\"page-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.totalFilteredComics},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,WAAW,EAAC;IAACE,WAAW,EAAC;EAAQ,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,UAAU,EAAC;IAACI,KAAK,EAAC;MAAC,aAAa,EAAC,SAAS;MAAC,aAAa,EAAC,gBAAgB;MAAC,WAAW,EAAC;IAAE,CAAC;IAACC,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,IAAG,CAACA,MAAM,CAACC,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,IAAEV,GAAG,CAACW,EAAE,CAACH,MAAM,CAACI,OAAO,EAAC,OAAO,EAAC,EAAE,EAACJ,MAAM,CAACK,GAAG,EAAC,OAAO,CAAC,EAAC,OAAO,IAAI;QAAC,OAAOb,GAAG,CAACc,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAAA;IAAC,CAAC;IAACC,KAAK,EAAC;MAACC,KAAK,EAAElB,GAAG,CAACmB,aAAc;MAACC,QAAQ,EAAC,SAAAA,CAAUC,GAAG,EAAE;QAACrB,GAAG,CAACmB,aAAa,GAACE,GAAG;MAAA,CAAC;MAACC,UAAU,EAAC;IAAe;EAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,WAAW,EAAC;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC,SAAS;MAAC,MAAM,EAAC;IAAgB,CAAC;IAACC,EAAE,EAAC;MAAC,OAAO,EAACN,GAAG,CAACc;IAAY;EAAC,CAAC,EAAC,CAACd,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,WAAW,EAAC;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC;IAAM;EAAC,CAAC,EAAC,CAACL,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,WAAW,EAAC;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC;IAAS;EAAC,CAAC,EAAC,CAACL,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,SAAS,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAACH,GAAG,CAACuB,EAAE,CAAEvB,GAAG,CAACwB,cAAc,EAAE,UAASC,KAAK,EAAC;IAAC,OAAOxB,EAAE,CAAC,KAAK,EAAC;MAACY,GAAG,EAACY,KAAK,CAACC,EAAE;MAACvB,WAAW,EAAC,YAAY;MAACG,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAqB,CAASnB,MAAM,EAAC;UAAC,OAAOR,GAAG,CAAC4B,UAAU,CAACH,KAAK,CAACC,EAAE,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACzB,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACI,KAAK,EAAC;QAAC,KAAK,EAACoB,KAAK,CAACI,KAAK;QAAC,KAAK,EAACJ,KAAK,CAACK;MAAK;IAAC,CAAC,CAAC,EAAC7B,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAe,CAAC,EAAC,CAACF,EAAE,CAAC,WAAW,EAAC;MAACI,KAAK,EAAC;QAAC,MAAM,EAAC,SAAS;QAAC,MAAM,EAAC;MAAO;IAAC,CAAC,EAAC,CAACL,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAY,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;MAACE,WAAW,EAAC;IAAa,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAAC+B,EAAE,CAACN,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC7B,EAAE,CAAC,GAAG,EAAC;MAACE,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,GAACJ,GAAG,CAAC+B,EAAE,CAACN,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC,CAAC,EAAC/B,EAAE,CAAC,GAAG,EAAC;MAACE,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAAC+B,EAAE,CAACN,KAAK,CAACQ,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAAChC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,EAAC,CAACF,EAAE,CAAC,eAAe,EAAC;IAACI,KAAK,EAAC;MAAC,cAAc,EAACL,GAAG,CAACkC,WAAW;MAAC,YAAY,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAAC,WAAW,EAAClC,GAAG,CAACmC,QAAQ;MAAC,QAAQ,EAAC,yCAAyC;MAAC,OAAO,EAACnC,GAAG,CAACoC;IAAmB,CAAC;IAAC9B,EAAE,EAAC;MAAC,aAAa,EAACN,GAAG,CAACqC,gBAAgB;MAAC,gBAAgB,EAACrC,GAAG,CAACsC;IAAmB;EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AACt/D,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASxC,MAAM,EAAEwC,eAAe", "ignoreList": []}]}