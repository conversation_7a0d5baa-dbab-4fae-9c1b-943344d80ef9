{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/views/ComicList.vue", "mtime": 1748420781598}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/vue-loader/lib/index.js", "mtime": 1748420069560}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "searchKeyword", "currentPage", "pageSize", "totalComics", "comics", "id", "title", "cover", "latestChapter", "updateTime", "computed", "filteredComics", "filtered", "filter", "comic", "toLowerCase", "includes", "length", "start", "end", "slice", "methods", "searchComics", "goToDetail", "comicId", "$router", "push", "params", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/ComicList.vue"], "sourcesContent": ["<template>\n  <div class=\"comic-list\">\n    <!-- 顶部导航栏 -->\n    <el-header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <h2>漫画网站</h2>\n        </div>\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索漫画...\"\n            prefix-icon=\"el-icon-search\"\n            @keyup.enter=\"searchComics\"\n            clearable\n          />\n          <el-button type=\"primary\" @click=\"searchComics\" icon=\"el-icon-search\">搜索</el-button>\n        </div>\n        <div class=\"user-actions\">\n          <el-button type=\"text\">登录</el-button>\n          <el-button type=\"primary\">注册</el-button>\n        </div>\n      </div>\n    </el-header>\n\n    <!-- 主要内容区 -->\n    <el-main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <!-- 漫画卡片网格 -->\n        <div class=\"comic-grid\">\n          <div\n            v-for=\"comic in filteredComics\"\n            :key=\"comic.id\"\n            class=\"comic-card\"\n            @click=\"goToDetail(comic.id)\"\n          >\n            <div class=\"comic-cover\">\n              <img :src=\"comic.cover\" :alt=\"comic.title\" />\n              <div class=\"comic-overlay\">\n                <el-button type=\"primary\" size=\"small\">查看详情</el-button>\n              </div>\n            </div>\n            <div class=\"comic-info\">\n              <h3 class=\"comic-title\">{{ comic.title }}</h3>\n              <p class=\"comic-latest\">最新: {{ comic.latestChapter }}</p>\n              <p class=\"comic-update\">{{ comic.updateTime }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页控件 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[12, 24, 36, 48]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalComics\"\n          />\n        </div>\n      </div>\n    </el-main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ComicList',\n  data() {\n    return {\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 12,\n      totalComics: 0,\n      comics: [\n        {\n          id: 1,\n          title: '进击的巨人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',\n          latestChapter: '第139话',\n          updateTime: '2024-01-15'\n        },\n        {\n          id: 2,\n          title: '鬼灭之刃',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',\n          latestChapter: '第205话',\n          updateTime: '2024-01-14'\n        },\n        {\n          id: 3,\n          title: '海贼王',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',\n          latestChapter: '第1100话',\n          updateTime: '2024-01-13'\n        },\n        {\n          id: 4,\n          title: '火影忍者',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',\n          latestChapter: '第700话',\n          updateTime: '2024-01-12'\n        },\n        {\n          id: 5,\n          title: '龙珠',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',\n          latestChapter: '第519话',\n          updateTime: '2024-01-11'\n        },\n        {\n          id: 6,\n          title: '死神',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',\n          latestChapter: '第686话',\n          updateTime: '2024-01-10'\n        },\n        {\n          id: 7,\n          title: '我的英雄学院',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',\n          latestChapter: '第390话',\n          updateTime: '2024-01-09'\n        },\n        {\n          id: 8,\n          title: '东京喰种',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',\n          latestChapter: '第179话',\n          updateTime: '2024-01-08'\n        },\n        {\n          id: 9,\n          title: '约定的梦幻岛',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',\n          latestChapter: '第181话',\n          updateTime: '2024-01-07'\n        },\n        {\n          id: 10,\n          title: '咒术回战',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',\n          latestChapter: '第245话',\n          updateTime: '2024-01-06'\n        },\n        {\n          id: 11,\n          title: '链锯人',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',\n          latestChapter: '第150话',\n          updateTime: '2024-01-05'\n        },\n        {\n          id: 12,\n          title: '间谍过家家',\n          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',\n          latestChapter: '第95话',\n          updateTime: '2024-01-04'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredComics() {\n      let filtered = this.comics\n      if (this.searchKeyword) {\n        filtered = this.comics.filter(comic =>\n          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      this.totalComics = filtered.length\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return filtered.slice(start, end)\n    }\n  },\n  methods: {\n    searchComics() {\n      this.currentPage = 1\n    },\n    goToDetail(comicId) {\n      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.currentPage = 1\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comic-list {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0;\n  height: 70px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n  margin: 0 40px;\n}\n\n.search-box .el-input {\n  flex: 1;\n}\n\n.user-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.main-content {\n  padding: 20px;\n}\n\n.content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.comic-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.comic-card {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.comic-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);\n}\n\n.comic-cover {\n  position: relative;\n  height: 280px;\n  overflow: hidden;\n}\n\n.comic-cover img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.comic-card:hover .comic-cover img {\n  transform: scale(1.05);\n}\n\n.comic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.comic-card:hover .comic-overlay {\n  opacity: 1;\n}\n\n.comic-info {\n  padding: 15px;\n}\n\n.comic-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.comic-latest {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.comic-update {\n  font-size: 12px;\n  color: #999;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 移动端响应式 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    height: auto;\n    padding: 10px;\n    gap: 10px;\n  }\n\n  .search-box {\n    margin: 0;\n    max-width: 100%;\n  }\n\n  .comic-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 15px;\n  }\n\n  .comic-cover {\n    height: 200px;\n  }\n\n  .comic-info {\n    padding: 10px;\n  }\n\n  .comic-title {\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 480px) {\n  .comic-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .main-content {\n    padding: 10px;\n  }\n}\n</style>\n"], "mappings": ";;;AAoEA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA,GACA;QACAC,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA,GACA;QACAJ,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,eAAA;MACA,IAAAC,QAAA,QAAAR,MAAA;MACA,SAAAJ,aAAA;QACAY,QAAA,QAAAR,MAAA,CAAAS,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAR,KAAA,CAAAS,WAAA,GAAAC,QAAA,MAAAhB,aAAA,CAAAe,WAAA,GACA;MACA;MACA,KAAAZ,WAAA,GAAAS,QAAA,CAAAK,MAAA;MACA,MAAAC,KAAA,SAAAjB,WAAA,aAAAC,QAAA;MACA,MAAAiB,GAAA,GAAAD,KAAA,QAAAhB,QAAA;MACA,OAAAU,QAAA,CAAAQ,KAAA,CAAAF,KAAA,EAAAC,GAAA;IACA;EACA;EACAE,OAAA;IACAC,aAAA;MACA,KAAArB,WAAA;IACA;IACAsB,WAAAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAA5B,IAAA;QAAA6B,MAAA;UAAAtB,EAAA,EAAAmB;QAAA;MAAA;IACA;IACAI,iBAAAC,GAAA;MACA,KAAA3B,QAAA,GAAA2B,GAAA;MACA,KAAA5B,WAAA;IACA;IACA6B,oBAAAD,GAAA;MACA,KAAA5B,WAAA,GAAA4B,GAAA;IACA;EACA;AACA", "ignoreList": []}]}