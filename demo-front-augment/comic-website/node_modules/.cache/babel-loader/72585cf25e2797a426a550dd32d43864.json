{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/eslint-loader/index.js??ref--14-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/router/index.js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/router/index.js", "mtime": 1748420169180}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/thread-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/eslint-loader/index.js", "mtime": 1748420066355}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVlUm91dGVyIGZyb20gJ3Z1ZS1yb3V0ZXInOwppbXBvcnQgQ29taWNMaXN0IGZyb20gJy4uL3ZpZXdzL0NvbWljTGlzdC52dWUnOwppbXBvcnQgQ29taWNEZXRhaWwgZnJvbSAnLi4vdmlld3MvQ29taWNEZXRhaWwudnVlJzsKaW1wb3J0IENvbWljUmVhZGVyIGZyb20gJy4uL3ZpZXdzL0NvbWljUmVhZGVyLnZ1ZSc7ClZ1ZS51c2UoVnVlUm91dGVyKTsKY29uc3Qgcm91dGVzID0gW3sKICBwYXRoOiAnLycsCiAgbmFtZTogJ0NvbWljTGlzdCcsCiAgY29tcG9uZW50OiBDb21pY0xpc3QKfSwgewogIHBhdGg6ICcvY29taWMvOmlkJywKICBuYW1lOiAnQ29taWNEZXRhaWwnLAogIGNvbXBvbmVudDogQ29taWNEZXRhaWwsCiAgcHJvcHM6IHRydWUKfSwgewogIHBhdGg6ICcvY29taWMvOmlkL2NoYXB0ZXIvOmNoYXB0ZXJJZCcsCiAgbmFtZTogJ0NvbWljUmVhZGVyJywKICBjb21wb25lbnQ6IENvbWljUmVhZGVyLAogIHByb3BzOiB0cnVlCn1dOwpjb25zdCByb3V0ZXIgPSBuZXcgVnVlUm91dGVyKHsKICBtb2RlOiAnaGlzdG9yeScsCiAgYmFzZTogcHJvY2Vzcy5lbnYuQkFTRV9VUkwsCiAgcm91dGVzCn0pOwpleHBvcnQgZGVmYXVsdCByb3V0ZXI7"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ComicList", "ComicDetail", "ComicReader", "use", "routes", "path", "name", "component", "props", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport ComicList from '../views/ComicList.vue'\nimport ComicDetail from '../views/ComicDetail.vue'\nimport ComicReader from '../views/ComicReader.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'ComicList',\n    component: ComicList\n  },\n  {\n    path: '/comic/:id',\n    name: 'ComicDetail',\n    component: ComicDetail,\n    props: true\n  },\n  {\n    path: '/comic/:id/chapter/:chapterId',\n    name: 'ComicReader',\n    component: ComicReader,\n    props: true\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAElDJ,GAAG,CAACK,GAAG,CAACJ,SAAS,CAAC;AAElB,MAAMK,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEP;AACb,CAAC,EACD;EACEK,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEN,WAAW;EACtBO,KAAK,EAAE;AACT,CAAC,EACD;EACEH,IAAI,EAAE,+BAA+B;EACrCC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL,WAAW;EACtBM,KAAK,EAAE;AACT,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIV,SAAS,CAAC;EAC3BW,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BV;AACF,CAAC,CAAC;AAEF,eAAeK,MAAM", "ignoreList": []}]}