{"remainingRequest": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/eslint-loader/index.js??ref--14-0!/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/main.js", "dependencies": [{"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/main.js", "mtime": 1748420149885}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/babel.config.js", "mtime": 1748420427478}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/cache-loader/dist/cjs.js", "mtime": 1748420066077}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/babel-loader/lib/index.js", "mtime": 1748420068956}, {"path": "/Users/<USER>/WK/devRead/demo-front-augment/comic-website/node_modules/eslint-loader/index.js", "mtime": 1748420066355}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnLi9yb3V0ZXInOwppbXBvcnQgRWxlbWVudFVJIGZyb20gJ2VsZW1lbnQtdWknOwppbXBvcnQgJ2VsZW1lbnQtdWkvbGliL3RoZW1lLWNoYWxrL2luZGV4LmNzcyc7ClZ1ZS5jb25maWcucHJvZHVjdGlvblRpcCA9IGZhbHNlOwpWdWUudXNlKEVsZW1lbnRVSSk7Cm5ldyBWdWUoewogIHJvdXRlciwKICByZW5kZXI6IGggPT4gaChBcHApCn0pLiRtb3VudCgnI2FwcCcpOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "ElementUI", "config", "productionTip", "use", "render", "h", "$mount"], "sources": ["/Users/<USER>/WK/devRead/demo-front-augment/comic-website/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\n\nVue.config.productionTip = false\n\nVue.use(ElementUI)\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAE7CH,GAAG,CAACI,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCL,GAAG,CAACM,GAAG,CAACH,SAAS,CAAC;AAElB,IAAIH,GAAG,CAAC;EACNE,MAAM;EACNK,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACP,GAAG;AACpB,CAAC,CAAC,CAACQ,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}