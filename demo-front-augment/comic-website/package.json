{"name": "comic-website", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "npm run serve", "start": "npm run serve"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.1", "element-ui": "^2.15.14", "axios": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}