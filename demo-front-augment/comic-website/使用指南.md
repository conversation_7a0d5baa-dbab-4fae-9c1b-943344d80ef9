# 漫画网站使用指南

## 🚀 快速开始

### 启动项目
```bash
# 方法1：标准启动
npm run serve

# 方法2：开发模式
npm run dev

# 方法3：直接启动
npm start
```

启动后自动打开浏览器访问：`http://localhost:8080`

## 📱 功能演示

### 1. 首页漫画列表
- **浏览漫画**：查看12个精选漫画，包括进击的巨人、鬼灭之刃等热门作品
- **搜索功能**：在搜索框输入关键词，实时过滤漫画
- **分页浏览**：底部可调整每页显示数量（12/24/36/48）
- **响应式**：PC端显示4-5列，移动端自动调整为2列

### 2. 漫画详情页
- **点击任意漫画卡片**进入详情页
- **查看信息**：作者、状态、简介等详细信息
- **章节列表**：10个章节，支持正序/倒序切换
- **开始阅读**：点击"开始阅读"或任意章节进入阅读页

### 3. 漫画阅读页 ⭐ 核心功能

#### 阅读模式切换
**默认：上下拼接模式**
- 所有页面垂直连续显示
- 适合快速浏览和连续阅读
- 支持滚动操作

**切换：左右滑动模式**
- 点击顶部"左右滑动"按钮切换
- 单页显示，类似翻书体验
- 显示页码（如：3/8）

#### 操作方式

**PC端操作：**
- 🖱️ **鼠标点击**：左右导航按钮
- ⌨️ **键盘快捷键**：
  - `←` 上一页
  - `→` 下一页  
  - `空格` 下一页
- 🖱️ **点击图片区域**：显示/隐藏控制栏

**移动端操作：**
- 👆 **触摸手势**：
  - 左滑 → 下一页
  - 右滑 → 上一页
- 👆 **点击屏幕**：显示/隐藏控制栏

#### 其他功能
- **全屏阅读**：点击全屏按钮或按F11
- **章节跳转**：底部下拉选择器快速切换章节
- **上一章/下一章**：底部导航按钮
- **偏好记忆**：自动保存选择的阅读模式

## 🎯 测试建议

### 基础功能测试
1. **列表页测试**
   - 搜索"进击"查看过滤效果
   - 调整分页大小查看布局变化
   - 点击不同漫画查看详情

2. **详情页测试**
   - 切换章节排序（正序/倒序）
   - 点击不同章节进入阅读
   - 测试返回按钮

3. **阅读页测试**
   - 切换两种阅读模式
   - 测试各种翻页方式
   - 尝试全屏功能
   - 测试章节跳转

### 响应式测试
1. **PC端测试**
   - 调整浏览器窗口大小
   - 测试键盘快捷键
   - 查看悬停效果

2. **移动端测试**
   - 使用浏览器开发者工具切换到移动设备模式
   - 测试触摸手势（如果支持）
   - 查看移动端布局适配

## 🖼️ 图片说明

项目使用 Unsplash 在线图片服务：

- **漫画封面**：使用同一张高质量图片的不同色调变化
- **阅读页面**：每个章节使用不同色调的图片组合
- **响应式**：图片会根据设备尺寸自动调整
- **加载优化**：支持图片加载失败的错误处理

### 图片特点
- 高质量：来自专业摄影平台
- 一致性：使用相同基础图片保证视觉统一
- 多样性：通过色调变化模拟不同漫画内容
- 稳定性：在线服务，无需本地存储

## 🔧 自定义配置

### 修改漫画数据
在 `src/views/ComicList.vue` 中修改 `comics` 数组：
```javascript
{
  id: 13,
  title: '新漫画',
  cover: '图片URL',
  latestChapter: '第XX话',
  updateTime: '2024-01-XX'
}
```

### 修改默认阅读模式
在 `src/views/ComicReader.vue` 中修改：
```javascript
readingMode: 'vertical', // 改为 'horizontal'
```

### 添加更多章节
在 `src/views/ComicDetail.vue` 中的 `chapters` 数组添加：
```javascript
{ id: 11, number: '第11话', title: '新章节', date: '2024-01-XX' }
```

## 🐛 常见问题

### Q: 图片加载缓慢？
A: 这是正常现象，因为使用的是在线图片服务。首次加载可能较慢，后续会有缓存。

### Q: 触摸手势不工作？
A: 在PC浏览器中，可以使用开发者工具的设备模拟模式来测试触摸功能。

### Q: 全屏模式无法退出？
A: 按ESC键或点击退出全屏按钮。

### Q: 阅读模式切换没反应？
A: 检查浏览器控制台是否有错误，或尝试刷新页面。

## 📞 技术支持

如果遇到问题：
1. 检查浏览器控制台错误信息
2. 确认Node.js和npm版本符合要求
3. 尝试清除浏览器缓存
4. 重新安装依赖：`rm -rf node_modules && npm install --legacy-peer-deps`

---

**祝您使用愉快！** 📚✨
