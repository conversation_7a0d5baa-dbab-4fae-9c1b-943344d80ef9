<template>
  <div class="comic-detail">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <el-button @click="goBack" icon="el-icon-arrow-left" type="text" class="back-btn">
          返回列表
        </el-button>
        <h2>{{ comic.title }}</h2>
      </div>
    </el-header>

    <!-- 主要内容 -->
    <el-main class="main-content">
      <div class="content-wrapper">
        <!-- 漫画信息区 -->
        <div class="comic-info-section">
          <div class="comic-cover-large">
            <img :src="comic.cover" :alt="comic.title" />
          </div>
          <div class="comic-details">
            <h1 class="comic-title">{{ comic.title }}</h1>
            <div class="comic-meta">
              <p><strong>作者:</strong> {{ comic.author }}</p>
              <p><strong>状态:</strong>
                <el-tag :type="comic.status === '连载中' ? 'success' : 'info'">
                  {{ comic.status }}
                </el-tag>
              </p>
              <p><strong>最新章节:</strong> {{ comic.latestChapter }}</p>
              <p><strong>更新时间:</strong> {{ comic.updateTime }}</p>
            </div>
            <div class="comic-description">
              <h3>简介</h3>
              <p>{{ comic.description }}</p>
            </div>
            <div class="action-buttons">
              <el-button type="primary" size="large" @click="startReading">
                开始阅读
              </el-button>
              <el-button size="large">收藏</el-button>
            </div>
          </div>
        </div>

        <!-- 章节列表区 -->
        <div class="chapters-section">
          <div class="section-header">
            <h3>章节列表</h3>
            <div class="sort-controls">
              <el-radio-group v-model="sortOrder" @change="sortChapters">
                <el-radio-button label="asc">正序</el-radio-button>
                <el-radio-button label="desc">倒序</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="chapters-grid">
            <div
              v-for="chapter in sortedChapters"
              :key="chapter.id"
              class="chapter-item"
              @click="readChapter(chapter.id)"
            >
              <div class="chapter-number">{{ chapter.number }}</div>
              <div class="chapter-title">{{ chapter.title }}</div>
              <div class="chapter-date">{{ chapter.date }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
export default {
  name: 'ComicDetail',
  props: ['id'],
  data() {
    return {
      sortOrder: 'desc',
      comic: {
        id: 1,
        title: '进击的巨人',
        author: '諫山創',
        status: '已完结',
        cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center',
        latestChapter: '第139话',
        updateTime: '2024-01-15',
        description: '《进击的巨人》是日本漫画家諫山創创作的漫画作品。故事讲述了人类与巨人之间的战斗，以及主人公艾伦·耶格尔等人为了人类的自由而奋斗的故事。作品以其独特的世界观、复杂的剧情和深刻的主题而广受好评。这部作品不仅在日本国内获得了巨大成功，在全球范围内也拥有大量粉丝，被誉为21世纪最优秀的漫画作品之一。'
      },
      chapters: [
        { id: 1, number: '第1话', title: '致两千年后的你', date: '2023-01-01' },
        { id: 2, number: '第2话', title: '那一天', date: '2023-01-02' },
        { id: 3, number: '第3话', title: '解散式之夜', date: '2023-01-03' },
        { id: 4, number: '第4话', title: '初阵', date: '2023-01-04' },
        { id: 5, number: '第5话', title: '心脏的跳动声', date: '2023-01-05' },
        { id: 6, number: '第6话', title: '少女所见的世界', date: '2023-01-06' },
        { id: 7, number: '第7话', title: '小小的刀刃', date: '2023-01-07' },
        { id: 8, number: '第8话', title: '咆哮', date: '2023-01-08' },
        { id: 9, number: '第9话', title: '左臂的去向', date: '2023-01-09' },
        { id: 10, number: '第10话', title: '应对', date: '2023-01-10' }
      ]
    }
  },
  computed: {
    sortedChapters() {
      const chapters = [...this.chapters]
      if (this.sortOrder === 'asc') {
        return chapters.sort((a, b) => a.id - b.id)
      } else {
        return chapters.sort((a, b) => b.id - a.id)
      }
    }
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'ComicList' })
    },
    startReading() {
      // 开始阅读第一章
      const firstChapter = this.sortOrder === 'asc' ?
        Math.min(...this.chapters.map(c => c.id)) :
        Math.max(...this.chapters.map(c => c.id))
      this.readChapter(firstChapter)
    },
    readChapter(chapterId) {
      this.$router.push({
        name: 'ComicReader',
        params: {
          id: this.id,
          chapterId: chapterId
        }
      })
    },
    sortChapters() {
      // 排序逻辑已在computed中处理
    }
  },
  mounted() {
    // 根据路由参数加载对应的漫画数据
    console.log('Comic ID:', this.id)
  }
}
</script>

<style scoped>
.comic-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 70px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 20px;
}

.back-btn {
  color: white !important;
  font-size: 16px;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.main-content {
  padding: 20px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.comic-info-section {
  display: flex;
  gap: 30px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.comic-cover-large {
  flex-shrink: 0;
}

.comic-cover-large img {
  width: 300px;
  height: 400px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.comic-details {
  flex: 1;
}

.comic-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.comic-meta {
  margin-bottom: 25px;
}

.comic-meta p {
  margin-bottom: 10px;
  font-size: 16px;
  color: #666;
}

.comic-description {
  margin-bottom: 30px;
}

.comic-description h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.comic-description p {
  line-height: 1.6;
  color: #666;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.chapters-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.section-header h3 {
  font-size: 20px;
  color: #333;
  margin: 0;
}

.chapters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.chapter-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-item:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}

.chapter-number {
  font-weight: bold;
  color: #667eea;
  min-width: 80px;
}

.chapter-title {
  flex: 1;
  margin-left: 15px;
  color: #333;
}

.chapter-date {
  font-size: 12px;
  color: #999;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .comic-info-section {
    flex-direction: column;
    padding: 20px;
  }

  .comic-cover-large img {
    width: 200px;
    height: 280px;
    margin: 0 auto;
    display: block;
  }

  .comic-title {
    font-size: 24px;
    text-align: center;
  }

  .chapters-grid {
    grid-template-columns: 1fr;
  }

  .chapter-item {
    padding: 12px;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px;
  }

  .comic-info-section,
  .chapters-section {
    padding: 15px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
