<template>
  <div class="comic-list">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>漫画网站</h2>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索漫画..."
            prefix-icon="el-icon-search"
            @keyup.enter="searchComics"
            clearable
          />
          <el-button type="primary" @click="searchComics" icon="el-icon-search">搜索</el-button>
        </div>
        <div class="user-actions">
          <el-button type="text">登录</el-button>
          <el-button type="primary">注册</el-button>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区 -->
    <el-main class="main-content">
      <div class="content-wrapper">
        <!-- 漫画卡片网格 -->
        <div class="comic-grid">
          <div
            v-for="comic in filteredComics"
            :key="comic.id"
            class="comic-card"
            @click="goToDetail(comic.id)"
          >
            <div class="comic-cover">
              <img :src="comic.cover" :alt="comic.title" />
              <div class="comic-overlay">
                <el-button type="primary" size="small">查看详情</el-button>
              </div>
            </div>
            <div class="comic-info">
              <h3 class="comic-title">{{ comic.title }}</h3>
              <p class="comic-latest">最新: {{ comic.latestChapter }}</p>
              <p class="comic-update">{{ comic.updateTime }}</p>
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[12, 24, 36, 48]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalFilteredComics"
          />
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
export default {
  name: 'ComicList',
  data() {
    return {
      searchKeyword: '',
      currentPage: 1,
      pageSize: 12,
      comics: [
        {
          id: 1,
          title: '进击的巨人',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center',
          latestChapter: '第139话',
          updateTime: '2024-01-15'
        },
        {
          id: 2,
          title: '鬼灭之刃',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=240',
          latestChapter: '第205话',
          updateTime: '2024-01-14'
        },
        {
          id: 3,
          title: '海贼王',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=30',
          latestChapter: '第1100话',
          updateTime: '2024-01-13'
        },
        {
          id: 4,
          title: '火影忍者',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=270',
          latestChapter: '第700话',
          updateTime: '2024-01-12'
        },
        {
          id: 5,
          title: '龙珠',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=0',
          latestChapter: '第519话',
          updateTime: '2024-01-11'
        },
        {
          id: 6,
          title: '死神',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=180',
          latestChapter: '第686话',
          updateTime: '2024-01-10'
        },
        {
          id: 7,
          title: '我的英雄学院',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=120',
          latestChapter: '第390话',
          updateTime: '2024-01-09'
        },
        {
          id: 8,
          title: '东京喰种',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=300',
          latestChapter: '第179话',
          updateTime: '2024-01-08'
        },
        {
          id: 9,
          title: '约定的梦幻岛',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=60',
          latestChapter: '第181话',
          updateTime: '2024-01-07'
        },
        {
          id: 10,
          title: '咒术回战',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=210',
          latestChapter: '第245话',
          updateTime: '2024-01-06'
        },
        {
          id: 11,
          title: '链锯人',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=330',
          latestChapter: '第150话',
          updateTime: '2024-01-05'
        },
        {
          id: 12,
          title: '间谍过家家',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=280&fit=crop&crop=center&sat=-100&hue=90',
          latestChapter: '第95话',
          updateTime: '2024-01-04'
        }
      ]
    }
  },
  computed: {
    filteredComics() {
      let filtered = this.comics
      if (this.searchKeyword) {
        filtered = this.comics.filter(comic =>
          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return filtered.slice(start, end)
    },
    totalFilteredComics() {
      let filtered = this.comics
      if (this.searchKeyword) {
        filtered = this.comics.filter(comic =>
          comic.title.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      return filtered.length
    }
  },
  methods: {
    searchComics() {
      this.currentPage = 1
    },
    goToDetail(comicId) {
      this.$router.push({ name: 'ComicDetail', params: { id: comicId } })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped>
.comic-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 70px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-box .el-input {
  flex: 1;
}

.user-actions {
  display: flex;
  gap: 10px;
}

.main-content {
  padding: 20px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.comic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.comic-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.comic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
}

.comic-cover {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.comic-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.comic-card:hover .comic-cover img {
  transform: scale(1.05);
}

.comic-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.comic-card:hover .comic-overlay {
  opacity: 1;
}

.comic-info {
  padding: 15px;
}

.comic-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comic-latest {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.comic-update {
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
  .header {
    height: auto;
    min-height: 70px;
  }

  .header-content {
    flex-direction: column;
    height: auto;
    padding: 15px;
    gap: 15px;
  }

  .logo h2 {
    font-size: 20px;
    text-align: center;
  }

  .search-box {
    margin: 0;
    max-width: 100%;
    width: 100%;
  }

  .search-box .el-input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .user-actions {
    justify-content: center;
    width: 100%;
  }

  .main-content {
    padding: 15px;
  }

  .comic-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .comic-cover {
    height: 200px;
  }

  .comic-info {
    padding: 12px;
  }

  .comic-title {
    font-size: 14px;
    line-height: 1.3;
  }

  .comic-latest {
    font-size: 13px;
  }

  .comic-update {
    font-size: 11px;
  }

  .pagination-wrapper {
    margin-top: 30px;
  }

  .pagination-wrapper .el-pagination {
    text-align: center;
  }

  .pagination-wrapper .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 12px;
    gap: 12px;
  }

  .logo h2 {
    font-size: 18px;
  }

  .main-content {
    padding: 12px;
  }

  .comic-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .comic-cover {
    height: 180px;
  }

  .comic-info {
    padding: 10px;
  }

  .comic-title {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .comic-latest {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .comic-update {
    font-size: 10px;
  }

  .user-actions .el-button {
    padding: 8px 15px;
    font-size: 14px;
  }

  .search-box .el-button {
    padding: 10px 15px;
  }

  /* 分页组件移动端优化 */
  .pagination-wrapper .el-pagination {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }

  .pagination-wrapper .el-pagination .el-pagination__sizes {
    margin: 0 5px 10px 0;
  }

  .pagination-wrapper .el-pagination .el-pagination__total {
    margin: 0 5px 10px 0;
  }

  .pagination-wrapper .el-pagination .el-pager {
    margin: 0 5px 10px 0;
  }

  .pagination-wrapper .el-pagination .btn-prev,
  .pagination-wrapper .el-pagination .btn-next {
    margin: 0 2px 10px 2px;
  }

  .pagination-wrapper .el-pagination .el-pagination__jump {
    margin: 0 5px 10px 0;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .header-content {
    padding: 10px;
  }

  .main-content {
    padding: 10px;
  }

  .comic-grid {
    gap: 8px;
  }

  .comic-cover {
    height: 160px;
  }

  .comic-info {
    padding: 8px;
  }

  .comic-title {
    font-size: 12px;
  }

  .comic-latest {
    font-size: 11px;
  }

  .comic-update {
    font-size: 10px;
  }
}
</style>
