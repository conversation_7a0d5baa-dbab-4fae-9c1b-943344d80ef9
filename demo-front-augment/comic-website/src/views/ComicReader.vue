<template>
  <div class="comic-reader" :class="{ 'fullscreen': isFullscreen }">
    <!-- 顶部控制栏 -->
    <div class="top-controls" :class="{ 'hidden': controlsHidden }">
      <div class="controls-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" type="text" class="control-btn">
          返回
        </el-button>
        <span class="chapter-info">{{ currentChapter.title }}</span>
      </div>
      <div class="controls-right">
        <el-button-group class="reading-mode-toggle">
          <el-button
            :type="readingMode === 'horizontal' ? 'primary' : ''"
            @click="setReadingMode('horizontal')"
            size="small"
          >
            左右滑动
          </el-button>
          <el-button
            :type="readingMode === 'vertical' ? 'primary' : ''"
            @click="setReadingMode('vertical')"
            size="small"
          >
            上下拼接
          </el-button>
        </el-button-group>
        <el-button @click="toggleFullscreen" :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'" type="text" class="control-btn">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
      </div>
    </div>

    <!-- 阅读区域 -->
    <div
      class="reading-area"
      @click="toggleControls"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
      @touchmove="handleTouchMove"
    >
      <!-- 水平滑动模式 -->
      <div v-if="readingMode === 'horizontal'" class="horizontal-reader">
        <div class="image-container" ref="imageContainer">
          <img
            v-for="(image, index) in currentChapter.images"
            :key="index"
            :src="image"
            :alt="`第${currentPage + 1}页`"
            class="comic-image"
            :class="{ 'active': index === currentPage }"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>

        <!-- 左右导航按钮 -->
        <div class="nav-buttons">
          <el-button
            v-if="currentPage > 0"
            @click.stop="prevPage"
            class="nav-btn nav-btn-left"
            icon="el-icon-arrow-left"
            circle
          />
          <el-button
            v-if="currentPage < currentChapter.images.length - 1"
            @click.stop="nextPage"
            class="nav-btn nav-btn-right"
            icon="el-icon-arrow-right"
            circle
          />
        </div>
      </div>

      <!-- 垂直拼接模式 -->
      <div v-else class="vertical-reader" ref="verticalReader">
        <img
          v-for="(image, index) in currentChapter.images"
          :key="index"
          :src="image"
          :alt="`第${index + 1}页`"
          class="comic-image-vertical"
          @load="onImageLoad"
          @error="onImageError"
        />
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-controls" :class="{ 'hidden': controlsHidden }">
      <div class="page-info" v-if="readingMode === 'horizontal'">
        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>
      </div>

      <div class="chapter-navigation">
        <el-button
          @click="prevChapter"
          :disabled="!hasPrevChapter"
          size="small"
        >
          上一章
        </el-button>

        <el-select v-model="currentChapterId" @change="changeChapter" size="small" style="width: 200px;">
          <el-option
            v-for="chapter in allChapters"
            :key="chapter.id"
            :label="chapter.title"
            :value="chapter.id"
          />
        </el-select>

        <el-button
          @click="nextChapter"
          :disabled="!hasNextChapter"
          size="small"
        >
          下一章
        </el-button>
      </div>

      <div class="quick-actions">
        <el-button @click="goHome" size="small">回到首页</el-button>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-text>加载中...</el-loading-text>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComicReader',
  props: ['id', 'chapterId'],
  data() {
    return {
      readingMode: 'vertical', // 默认垂直拼接模式
      currentPage: 0,
      currentChapterId: parseInt(this.chapterId),
      isFullscreen: false,
      controlsHidden: false,
      loading: false,
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      currentChapter: {
        id: 1,
        title: '第1话 致两千年后的你',
        images: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'
        ]
      },
      allChapters: [
        { id: 1, title: '第1话 致两千年后的你' },
        { id: 2, title: '第2话 那一天' },
        { id: 3, title: '第3话 解散式之夜' },
        { id: 4, title: '第4话 初阵' },
        { id: 5, title: '第5话 心脏的跳动声' }
      ]
    }
  },
  computed: {
    hasPrevChapter() {
      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
      return currentIndex > 0
    },
    hasNextChapter() {
      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
      return currentIndex < this.allChapters.length - 1
    }
  },
  methods: {
    setReadingMode(mode) {
      this.readingMode = mode
      if (mode === 'horizontal') {
        this.currentPage = 0
      }
      // 保存用户偏好到localStorage
      localStorage.setItem('readingMode', mode)
    },
    toggleControls() {
      this.controlsHidden = !this.controlsHidden
    },
    toggleFullscreen() {
      if (!this.isFullscreen) {
        this.enterFullscreen()
      } else {
        this.exitFullscreen()
      }
    },
    enterFullscreen() {
      const element = this.$el
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
      this.isFullscreen = true
    },
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
      this.isFullscreen = false
    },
    prevPage() {
      if (this.currentPage > 0) {
        this.currentPage--
        this.updateImageDisplay()
      }
    },
    nextPage() {
      if (this.currentPage < this.currentChapter.images.length - 1) {
        this.currentPage++
        this.updateImageDisplay()
      } else {
        // 自动跳转到下一章
        if (this.hasNextChapter) {
          this.nextChapter()
        }
      }
    },
    updateImageDisplay() {
      if (this.readingMode === 'horizontal') {
        const container = this.$refs.imageContainer
        if (container) {
          const imageWidth = container.clientWidth
          container.scrollLeft = this.currentPage * imageWidth
        }
      }
    },
    prevChapter() {
      if (this.hasPrevChapter) {
        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
        const prevChapter = this.allChapters[currentIndex - 1]
        this.changeChapter(prevChapter.id)
      }
    },
    nextChapter() {
      if (this.hasNextChapter) {
        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
        const nextChapter = this.allChapters[currentIndex + 1]
        this.changeChapter(nextChapter.id)
      }
    },
    changeChapter(chapterId) {
      this.loading = true
      this.currentChapterId = chapterId
      this.currentPage = 0

      // 模拟加载章节数据
      setTimeout(() => {
        const chapter = this.allChapters.find(c => c.id === chapterId)
        this.currentChapter = {
          ...chapter,
          images: [
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`
          ]
        }
        this.loading = false

        // 更新URL
        this.$router.replace({
          name: 'ComicReader',
          params: { id: this.id, chapterId: chapterId }
        })
      }, 1000)
    },
    goBack() {
      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })
    },
    goHome() {
      this.$router.push({ name: 'ComicList' })
    },
    onImageLoad() {
      // 图片加载完成
    },
    onImageError() {
      // 图片加载失败
      console.error('图片加载失败')
    },
    handleTouchStart(event) {
      if (event.touches.length === 1) {
        this.touchStartX = event.touches[0].clientX
        this.touchStartY = event.touches[0].clientY
        this.touchStartTime = Date.now()
      }
    },
    handleTouchMove(event) {
      // 防止页面滚动（仅在水平滑动模式下）
      if (this.readingMode === 'horizontal' && event.touches.length === 1) {
        const touchX = event.touches[0].clientX
        const touchY = event.touches[0].clientY
        const deltaX = Math.abs(touchX - this.touchStartX)
        const deltaY = Math.abs(touchY - this.touchStartY)

        // 如果水平滑动距离大于垂直滑动距离，阻止默认滚动
        if (deltaX > deltaY) {
          event.preventDefault()
        }
      }
    },
    handleTouchEnd(event) {
      if (!this.touchStartX || event.changedTouches.length !== 1) return

      const touchEndX = event.changedTouches[0].clientX
      const touchEndY = event.changedTouches[0].clientY
      const touchEndTime = Date.now()

      const deltaX = this.touchStartX - touchEndX
      const deltaY = this.touchStartY - touchEndY
      const deltaTime = touchEndTime - this.touchStartTime

      // 检查是否为有效的滑动手势
      const minSwipeDistance = 50 // 最小滑动距离
      const maxSwipeTime = 500 // 最大滑动时间
      const maxVerticalDistance = 100 // 最大垂直偏移

      if (Math.abs(deltaX) > minSwipeDistance &&
          Math.abs(deltaY) < maxVerticalDistance &&
          deltaTime < maxSwipeTime) {

        if (this.readingMode === 'horizontal') {
          // 水平滑动模式下的手势处理
          if (deltaX > 0) {
            // 向左滑动，下一页
            this.nextPage()
          } else {
            // 向右滑动，上一页
            this.prevPage()
          }
        }
      } else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
        // 点击手势，切换控制栏显示
        this.toggleControls()
      }

      // 重置触摸状态
      this.touchStartX = 0
      this.touchStartY = 0
      this.touchStartTime = 0
    },
    handleKeydown(event) {
      if (this.readingMode === 'horizontal') {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault()
            this.prevPage()
            break
          case 'ArrowRight':
            event.preventDefault()
            this.nextPage()
            break
          case ' ':
            event.preventDefault()
            this.nextPage()
            break
          case 'Escape':
            if (this.isFullscreen) {
              this.exitFullscreen()
            }
            break
        }
      }
    }
  },
  mounted() {
    // 从localStorage读取用户偏好的阅读模式
    const savedMode = localStorage.getItem('readingMode')
    if (savedMode) {
      this.readingMode = savedMode
    }

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement
    })

    // 监听webkit全屏状态变化（Safari）
    document.addEventListener('webkitfullscreenchange', () => {
      this.isFullscreen = !!document.webkitFullscreenElement
    })

    // 加载当前章节数据
    this.changeChapter(this.currentChapterId)
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('keydown', this.handleKeydown)
    document.removeEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement
    })
    document.removeEventListener('webkitfullscreenchange', () => {
      this.isFullscreen = !!document.webkitFullscreenElement
    })
  }
}
</script>

<style scoped>
.comic-reader {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.comic-reader.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

/* 顶部控制栏 */
.top-controls {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.top-controls.hidden {
  transform: translateY(-100%);
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-btn {
  color: white !important;
  font-size: 16px;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.chapter-info {
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.reading-mode-toggle {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* 阅读区域 */
.reading-area {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 水平滑动模式 */
.horizontal-reader {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.image-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.image-container::-webkit-scrollbar {
  display: none;
}

.comic-image {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
  display: none;
}

.comic-image.active {
  display: block;
}

/* 导航按钮 */
.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background-color: rgba(0, 0, 0, 0.6) !important;
  border: none !important;
  color: white !important;
  width: 50px;
  height: 50px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.nav-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 垂直拼接模式 */
.vertical-reader {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.comic-image-vertical {
  max-width: 100%;
  height: auto;
  margin-bottom: 5px;
  background-color: #000;
}

/* 底部控制栏 */
.bottom-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.bottom-controls.hidden {
  transform: translateY(100%);
}

.page-info {
  color: white;
  font-size: 14px;
  min-width: 80px;
}

.chapter-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.quick-actions {
  min-width: 80px;
  text-align: right;
}

/* 加载提示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  color: white;
  font-size: 18px;
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
  .top-controls {
    height: 55px;
    padding: 0 12px;
  }

  .bottom-controls {
    height: 55px;
    padding: 0 12px;
  }

  .controls-left {
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .controls-right {
    gap: 8px;
    flex-shrink: 0;
  }

  .control-btn {
    font-size: 14px;
    padding: 5px 8px;
  }

  .chapter-info {
    font-size: 13px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .reading-mode-toggle .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }

  .chapter-navigation {
    gap: 8px;
    flex: 1;
    justify-content: center;
  }

  .chapter-navigation .el-select {
    width: 140px !important;
  }

  .chapter-navigation .el-button {
    padding: 5px 10px;
    font-size: 12px;
  }

  .page-info {
    font-size: 13px;
    min-width: 60px;
  }

  .quick-actions .el-button {
    padding: 5px 10px;
    font-size: 12px;
  }

  .nav-btn {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .nav-buttons {
    padding: 0 15px;
  }

  .vertical-reader {
    padding: 10px 5px;
  }

  .comic-image-vertical {
    margin-bottom: 3px;
    max-width: calc(100% - 10px);
  }
}

@media (max-width: 480px) {
  .top-controls {
    height: 50px;
    padding: 0 10px;
    flex-wrap: wrap;
  }

  .controls-left {
    gap: 6px;
    order: 1;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .controls-right {
    gap: 6px;
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .control-btn {
    font-size: 12px;
    padding: 4px 6px;
  }

  .chapter-info {
    font-size: 12px;
    max-width: 100px;
    flex: 1;
  }

  .reading-mode-toggle {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .reading-mode-toggle .el-button {
    padding: 3px 6px;
    font-size: 10px;
    flex: 1;
  }

  .bottom-controls {
    height: auto;
    min-height: 50px;
    flex-direction: column;
    gap: 8px;
    padding: 8px 10px;
  }

  .page-info {
    order: 1;
    text-align: center;
    font-size: 12px;
    min-width: auto;
  }

  .chapter-navigation {
    order: 2;
    gap: 6px;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .chapter-navigation .el-select {
    width: 120px !important;
    margin: 0 5px;
  }

  .chapter-navigation .el-button {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 60px;
  }

  .quick-actions {
    order: 3;
    text-align: center;
    min-width: auto;
  }

  .quick-actions .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .nav-buttons {
    padding: 0 10px;
  }

  .vertical-reader {
    padding: 5px 3px;
  }

  .comic-image-vertical {
    margin-bottom: 2px;
    max-width: calc(100% - 6px);
  }

  .loading-overlay {
    font-size: 16px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .top-controls {
    height: 45px;
    padding: 0 8px;
  }

  .controls-left,
  .controls-right {
    gap: 4px;
  }

  .control-btn {
    font-size: 11px;
    padding: 3px 5px;
  }

  .chapter-info {
    font-size: 11px;
    max-width: 80px;
  }

  .reading-mode-toggle .el-button {
    padding: 2px 4px;
    font-size: 9px;
  }

  .bottom-controls {
    padding: 6px 8px;
    gap: 6px;
  }

  .page-info {
    font-size: 11px;
  }

  .chapter-navigation .el-select {
    width: 100px !important;
  }

  .chapter-navigation .el-button {
    padding: 3px 6px;
    font-size: 10px;
    min-width: 50px;
  }

  .quick-actions .el-button {
    padding: 3px 6px;
    font-size: 10px;
  }

  .nav-btn {
    width: 35px;
    height: 35px;
    font-size: 12px;
  }

  .nav-buttons {
    padding: 0 8px;
  }

  .vertical-reader {
    padding: 3px 2px;
  }

  .comic-image-vertical {
    margin-bottom: 1px;
    max-width: calc(100% - 4px);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .nav-btn {
    opacity: 0.7;
    background-color: rgba(0, 0, 0, 0.8) !important;
  }

  .nav-btn:active {
    opacity: 1;
    transform: scale(0.95);
  }

  .comic-image-vertical {
    margin-bottom: 0;
  }

  /* 增加触摸区域 */
  .control-btn,
  .reading-mode-toggle .el-button,
  .chapter-navigation .el-button {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
  }

  /* 防止双击缩放 */
  .reading-area {
    touch-action: pan-y pinch-zoom;
  }

  .horizontal-reader {
    touch-action: pan-y;
  }

  /* 优化滚动性能 */
  .vertical-reader {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .top-controls,
  .bottom-controls {
    height: 45px;
  }

  .top-controls {
    padding: 0 15px;
  }

  .bottom-controls {
    padding: 0 15px;
    flex-direction: row;
    justify-content: space-between;
  }

  .chapter-navigation {
    order: 2;
    flex: 1;
    max-width: 300px;
  }

  .page-info {
    order: 1;
  }

  .quick-actions {
    order: 3;
  }

  .vertical-reader {
    padding: 5px 10px;
  }
}
</style>
