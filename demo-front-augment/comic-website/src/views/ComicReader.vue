<template>
  <div class="comic-reader" :class="{ 'fullscreen': isFullscreen }">
    <!-- 顶部控制栏 -->
    <div class="top-controls" :class="{ 'hidden': controlsHidden }">
      <div class="controls-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" type="text" class="control-btn">
          返回
        </el-button>
        <span class="chapter-info">{{ currentChapter.title }}</span>
      </div>
      <div class="controls-right">
        <el-button-group class="reading-mode-toggle">
          <el-button
            :type="readingMode === 'horizontal' ? 'primary' : ''"
            @click="setReadingMode('horizontal')"
            size="small"
          >
            左右滑动
          </el-button>
          <el-button
            :type="readingMode === 'vertical' ? 'primary' : ''"
            @click="setReadingMode('vertical')"
            size="small"
          >
            上下拼接
          </el-button>
        </el-button-group>
        <el-button @click="toggleFullscreen" :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'" type="text" class="control-btn">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
      </div>
    </div>

    <!-- 阅读区域 -->
    <div class="reading-area" @click="toggleControls">
      <!-- 水平滑动模式 -->
      <div v-if="readingMode === 'horizontal'" class="horizontal-reader">
        <div class="image-container" ref="imageContainer">
          <img
            v-for="(image, index) in currentChapter.images"
            :key="index"
            :src="image"
            :alt="`第${currentPage + 1}页`"
            class="comic-image"
            :class="{ 'active': index === currentPage }"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>

        <!-- 左右导航按钮 -->
        <div class="nav-buttons">
          <el-button
            v-if="currentPage > 0"
            @click.stop="prevPage"
            class="nav-btn nav-btn-left"
            icon="el-icon-arrow-left"
            circle
          />
          <el-button
            v-if="currentPage < currentChapter.images.length - 1"
            @click.stop="nextPage"
            class="nav-btn nav-btn-right"
            icon="el-icon-arrow-right"
            circle
          />
        </div>
      </div>

      <!-- 垂直拼接模式 -->
      <div v-else class="vertical-reader" ref="verticalReader">
        <img
          v-for="(image, index) in currentChapter.images"
          :key="index"
          :src="image"
          :alt="`第${index + 1}页`"
          class="comic-image-vertical"
          @load="onImageLoad"
          @error="onImageError"
        />
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-controls" :class="{ 'hidden': controlsHidden }">
      <div class="page-info" v-if="readingMode === 'horizontal'">
        <span>{{ currentPage + 1 }} / {{ currentChapter.images.length }}</span>
      </div>

      <div class="chapter-navigation">
        <el-button
          @click="prevChapter"
          :disabled="!hasPrevChapter"
          size="small"
        >
          上一章
        </el-button>

        <el-select v-model="currentChapterId" @change="changeChapter" size="small" style="width: 200px;">
          <el-option
            v-for="chapter in allChapters"
            :key="chapter.id"
            :label="chapter.title"
            :value="chapter.id"
          />
        </el-select>

        <el-button
          @click="nextChapter"
          :disabled="!hasNextChapter"
          size="small"
        >
          下一章
        </el-button>
      </div>

      <div class="quick-actions">
        <el-button @click="goHome" size="small">回到首页</el-button>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-text>加载中...</el-loading-text>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComicReader',
  props: ['id', 'chapterId'],
  data() {
    return {
      readingMode: 'vertical', // 默认垂直拼接模式
      currentPage: 0,
      currentChapterId: parseInt(this.chapterId),
      isFullscreen: false,
      controlsHidden: false,
      loading: false,
      currentChapter: {
        id: 1,
        title: '第1话 致两千年后的你',
        images: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=240',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=30',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=270',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=0',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=180',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=120',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=300'
        ]
      },
      allChapters: [
        { id: 1, title: '第1话 致两千年后的你' },
        { id: 2, title: '第2话 那一天' },
        { id: 3, title: '第3话 解散式之夜' },
        { id: 4, title: '第4话 初阵' },
        { id: 5, title: '第5话 心脏的跳动声' }
      ]
    }
  },
  computed: {
    hasPrevChapter() {
      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
      return currentIndex > 0
    },
    hasNextChapter() {
      const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
      return currentIndex < this.allChapters.length - 1
    }
  },
  methods: {
    setReadingMode(mode) {
      this.readingMode = mode
      if (mode === 'horizontal') {
        this.currentPage = 0
      }
      // 保存用户偏好到localStorage
      localStorage.setItem('readingMode', mode)
    },
    toggleControls() {
      this.controlsHidden = !this.controlsHidden
    },
    toggleFullscreen() {
      if (!this.isFullscreen) {
        this.enterFullscreen()
      } else {
        this.exitFullscreen()
      }
    },
    enterFullscreen() {
      const element = this.$el
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
      this.isFullscreen = true
    },
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
      this.isFullscreen = false
    },
    prevPage() {
      if (this.currentPage > 0) {
        this.currentPage--
        this.updateImageDisplay()
      }
    },
    nextPage() {
      if (this.currentPage < this.currentChapter.images.length - 1) {
        this.currentPage++
        this.updateImageDisplay()
      } else {
        // 自动跳转到下一章
        if (this.hasNextChapter) {
          this.nextChapter()
        }
      }
    },
    updateImageDisplay() {
      if (this.readingMode === 'horizontal') {
        const container = this.$refs.imageContainer
        if (container) {
          const imageWidth = container.clientWidth
          container.scrollLeft = this.currentPage * imageWidth
        }
      }
    },
    prevChapter() {
      if (this.hasPrevChapter) {
        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
        const prevChapter = this.allChapters[currentIndex - 1]
        this.changeChapter(prevChapter.id)
      }
    },
    nextChapter() {
      if (this.hasNextChapter) {
        const currentIndex = this.allChapters.findIndex(c => c.id === this.currentChapterId)
        const nextChapter = this.allChapters[currentIndex + 1]
        this.changeChapter(nextChapter.id)
      }
    },
    changeChapter(chapterId) {
      this.loading = true
      this.currentChapterId = chapterId
      this.currentPage = 0

      // 模拟加载章节数据
      setTimeout(() => {
        const chapter = this.allChapters.find(c => c.id === chapterId)
        this.currentChapter = {
          ...chapter,
          images: [
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 60}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 120}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 180}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 240}`,
            `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=1200&fit=crop&crop=center&sat=-100&hue=${chapterId * 30 + 300}`
          ]
        }
        this.loading = false

        // 更新URL
        this.$router.replace({
          name: 'ComicReader',
          params: { id: this.id, chapterId: chapterId }
        })
      }, 1000)
    },
    goBack() {
      this.$router.push({ name: 'ComicDetail', params: { id: this.id } })
    },
    goHome() {
      this.$router.push({ name: 'ComicList' })
    },
    onImageLoad() {
      // 图片加载完成
    },
    onImageError() {
      // 图片加载失败
      console.error('图片加载失败')
    }
  },
  mounted() {
    // 从localStorage读取用户偏好的阅读模式
    const savedMode = localStorage.getItem('readingMode')
    if (savedMode) {
      this.readingMode = savedMode
    }

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement
    })

    // 加载当前章节数据
    this.changeChapter(this.currentChapterId)
  }
}
</script>

<style scoped>
.comic-reader {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.comic-reader.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

/* 顶部控制栏 */
.top-controls {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.top-controls.hidden {
  transform: translateY(-100%);
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-btn {
  color: white !important;
  font-size: 16px;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.chapter-info {
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.reading-mode-toggle {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* 阅读区域 */
.reading-area {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 水平滑动模式 */
.horizontal-reader {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.image-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.image-container::-webkit-scrollbar {
  display: none;
}

.comic-image {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
  display: none;
}

.comic-image.active {
  display: block;
}

/* 导航按钮 */
.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background-color: rgba(0, 0, 0, 0.6) !important;
  border: none !important;
  color: white !important;
  width: 50px;
  height: 50px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.nav-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 垂直拼接模式 */
.vertical-reader {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.comic-image-vertical {
  max-width: 100%;
  height: auto;
  margin-bottom: 5px;
  background-color: #000;
}

/* 底部控制栏 */
.bottom-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.bottom-controls.hidden {
  transform: translateY(100%);
}

.page-info {
  color: white;
  font-size: 14px;
  min-width: 80px;
}

.chapter-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.quick-actions {
  min-width: 80px;
  text-align: right;
}

/* 加载提示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  color: white;
  font-size: 18px;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .top-controls,
  .bottom-controls {
    padding: 0 10px;
  }

  .controls-left,
  .controls-right {
    gap: 10px;
  }

  .chapter-info {
    font-size: 14px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chapter-navigation {
    gap: 10px;
  }

  .chapter-navigation .el-select {
    width: 150px !important;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
  }

  .vertical-reader {
    padding: 10px 0;
  }

  .comic-image-vertical {
    margin-bottom: 2px;
  }
}

@media (max-width: 480px) {
  .top-controls {
    height: 50px;
  }

  .bottom-controls {
    height: 50px;
    flex-direction: column;
    gap: 5px;
    padding: 5px 10px;
  }

  .chapter-navigation {
    order: 1;
  }

  .page-info,
  .quick-actions {
    order: 2;
    min-width: auto;
  }

  .reading-mode-toggle .el-button {
    padding: 5px 8px;
    font-size: 12px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .nav-btn {
    opacity: 0.5;
  }

  .comic-image-vertical {
    margin-bottom: 0;
  }
}
</style>
