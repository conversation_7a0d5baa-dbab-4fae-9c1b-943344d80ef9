<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  /* 防止iOS Safari双击缩放 */
  touch-action: manipulation;
  /* 优化移动端滚动 */
  -webkit-overflow-scrolling: touch;
}

body {
  background-color: #f5f5f5;
  /* 防止移动端橡皮筋效果 */
  overscroll-behavior: none;
  /* 优化字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 防止移动端文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
}

/* 移动端输入框优化 */
input, textarea, select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Element UI 移动端优化 */
.el-button {
  /* 增加触摸区域 */
  min-height: 36px;
  /* 防止双击缩放 */
  touch-action: manipulation;
}

.el-input__inner {
  /* 防止iOS输入框缩放 */
  font-size: 16px;
  /* 优化移动端输入体验 */
  -webkit-appearance: none;
  appearance: none;
  border-radius: 4px;
}

.el-select .el-input__inner {
  cursor: pointer;
}

/* 分页组件移动端优化 */
.el-pagination {
  text-align: center;
}

.el-pagination .el-pager li {
  min-width: 32px;
  height: 32px;
  line-height: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-container {
    flex-direction: column;
  }

  /* Element UI 组件移动端调整 */
  .el-button {
    min-height: 40px;
    padding: 8px 15px;
  }

  .el-button--small {
    min-height: 36px;
    padding: 6px 12px;
  }

  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }

  .el-select .el-input__inner {
    height: 36px;
    line-height: 36px;
  }

  /* 优化弹窗在移动端的显示 */
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  .el-message-box {
    width: 90% !important;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .el-button {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    padding: 10px 15px;
    font-size: 14px;
  }

  .el-button--small {
    min-height: 40px;
    padding: 8px 12px;
    font-size: 13px;
  }

  .el-input__inner {
    height: 44px;
    line-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .el-pagination .el-pager li {
    min-width: 36px;
    height: 36px;
    line-height: 36px;
    margin: 0 2px;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    min-width: 36px;
    height: 36px;
    line-height: 36px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  /* 横屏时减少垂直空间占用 */
  .el-button {
    min-height: 36px;
    padding: 6px 12px;
  }

  .el-input__inner {
    height: 36px;
    line-height: 36px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 优化高分辨率屏幕的显示效果 */
  #app {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* 暗色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  /* 可以在这里添加暗色模式样式 */
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
