import Vue from 'vue'
import VueRouter from 'vue-router'
import ComicList from '../views/ComicList.vue'
import ComicDetail from '../views/ComicDetail.vue'
import ComicReader from '../views/ComicReader.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'ComicList',
    component: ComicList
  },
  {
    path: '/comic/:id',
    name: 'ComicDetail',
    component: ComicDetail,
    props: true
  },
  {
    path: '/comic/:id/chapter/:chapterId',
    name: 'ComicReader',
    component: ComicReader,
    props: true
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
