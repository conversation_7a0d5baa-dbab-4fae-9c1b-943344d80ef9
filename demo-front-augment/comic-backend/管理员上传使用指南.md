# 🎨 漫画管理后台 - 图片上传使用指南

## 📋 功能概述

漫画管理后台提供了一个简单易用的图片上传管理界面，方便管理员进行漫画图片资源的上传与管理。

## 🔐 访问方式

### 1. 直接访问（需要密码）
```
http://localhost:3001/api/admin
```
默认管理员密码：`admin123`

### 2. 带密码参数访问（直接进入）
```
http://localhost:3001/api/admin?password=admin123
```

### 3. 修改管理员密码
在 `.env` 文件中修改 `ADMIN_PASSWORD` 配置：
```env
ADMIN_PASSWORD=your_new_password
```

## 🚀 主要功能

### 1. 📊 统计信息
- **总文件数**：显示上传目录中的文件总数
- **总大小**：显示所有文件的总大小
- **今日上传**：显示今天上传的文件数量

### 2. 📁 单文件上传
- 支持上传单个图片文件
- 可以设置漫画标题和章节号
- 支持的格式：JPG, PNG, GIF, WEBP
- 最大文件大小：10MB

**使用步骤：**
1. 填写漫画标题（可选）
2. 填写章节号（可选）
3. 点击选择图片文件
4. 点击"上传文件"按钮

### 3. 📚 批量上传
- 支持同时上传多个图片文件
- 可以设置统一的漫画标题和章节号
- 最多支持50个文件同时上传
- 显示文件列表预览

**使用步骤：**
1. 填写漫画标题（可选）
2. 填写章节号（可选）
3. 点击选择多个图片文件
4. 查看文件列表，可以删除不需要的文件
5. 点击"批量上传"按钮

### 4. 🛠️ 管理操作
- **刷新统计**：更新统计信息
- **查看文件**：在新窗口中查看所有已上传的文件
- **清理临时文件**：删除超过1天的临时文件

## 📡 API 接口

### 管理员相关接口

#### 1. 获取管理员页面
```http
GET /api/admin?password=admin123
```

#### 2. 获取统计信息
```http
GET /api/admin/stats?password=admin123
```

响应示例：
```json
{
  "success": true,
  "data": {
    "totalFiles": 25,
    "totalSize": 15728640,
    "todayUploads": 5
  }
}
```

#### 3. 查看文件列表
```http
GET /api/admin/files?password=admin123
```

#### 4. 清理临时文件
```http
POST /api/admin/cleanup
Content-Type: application/json

{
  "password": "admin123"
}
```

### 上传相关接口

#### 1. 单文件上传
```http
POST /api/upload/single
Content-Type: multipart/form-data

{
  "image": [文件],
  "comicTitle": "漫画标题",
  "chapterNumber": 1
}
```

#### 2. 批量上传
```http
POST /api/upload/batch
Content-Type: multipart/form-data

{
  "images": [多个文件],
  "comicTitle": "漫画标题",
  "chapterNumber": 1
}
```

## 🔧 配置说明

### 环境变量配置（.env 文件）

```env
# 管理员配置
ADMIN_PASSWORD=admin123

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# 图片处理配置
IMAGE_QUALITY=80
THUMBNAIL_WIDTH=200
THUMBNAIL_HEIGHT=280
```

### 配置项说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `ADMIN_PASSWORD` | 管理员密码 | `admin123` |
| `UPLOAD_PATH` | 上传文件存储路径 | `./uploads` |
| `MAX_FILE_SIZE` | 最大文件大小（字节） | `10485760` (10MB) |
| `ALLOWED_FILE_TYPES` | 允许的文件类型 | `jpg,jpeg,png,gif,webp` |
| `IMAGE_QUALITY` | 图片压缩质量 | `80` |
| `THUMBNAIL_WIDTH` | 缩略图宽度 | `200` |
| `THUMBNAIL_HEIGHT` | 缩略图高度 | `280` |

## 📁 文件存储结构

```
uploads/
├── general/           # 通用上传文件
├── comic_cover/       # 漫画封面
├── chapter_page/      # 章节页面
└── temp_*            # 临时文件（会被自动清理）
```

## 🔒 安全特性

### 1. 密码保护
- 所有管理员功能都需要密码验证
- 支持通过环境变量自定义密码

### 2. 文件类型限制
- 只允许上传指定类型的图片文件
- 自动验证文件扩展名和MIME类型

### 3. 文件大小限制
- 限制单个文件最大大小
- 防止恶意大文件上传

### 4. 路径安全
- 防止路径遍历攻击
- 文件名自动清理和重命名

## 🚨 故障排除

### 1. 无法访问管理页面
- 检查服务器是否正常运行
- 确认密码是否正确
- 检查端口是否被占用

### 2. 文件上传失败
- 检查文件大小是否超过限制
- 确认文件类型是否支持
- 检查上传目录权限

### 3. 统计信息不准确
- 点击"刷新统计"按钮
- 检查上传目录是否存在
- 确认文件权限设置

### 4. 临时文件过多
- 使用"清理临时文件"功能
- 检查自动清理机制是否正常

## 📞 技术支持

如果遇到问题，请检查：

1. **服务器日志**：查看控制台输出的错误信息
2. **浏览器控制台**：检查前端JavaScript错误
3. **网络连接**：确认前后端通信正常
4. **文件权限**：确保上传目录有写入权限

## 🎯 使用建议

### 1. 文件命名规范
- 建议使用有意义的文件名
- 避免使用特殊字符
- 保持文件名简洁明了

### 2. 批量上传技巧
- 按章节分组上传
- 确保文件顺序正确
- 定期清理不需要的文件

### 3. 性能优化
- 避免同时上传过多大文件
- 定期清理临时文件
- 监控磁盘空间使用

---

**🎉 现在您可以开始使用漫画管理后台进行图片上传管理了！**
