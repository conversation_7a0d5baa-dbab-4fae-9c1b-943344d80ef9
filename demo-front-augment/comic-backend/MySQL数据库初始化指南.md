# 🗄️ MySQL数据库初始化指南

## 📋 概述

本指南详细说明如何为漫画网站系统初始化MySQL数据库，包括安装、配置、创建数据库结构和插入示例数据。

## 🛠️ MySQL安装

### macOS 安装

#### 方法1：使用 Homebrew（推荐）
```bash
# 安装 Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 MySQL 5.7
brew install mysql@5.7

# 启动 MySQL 服务
brew services start mysql@5.7

# 添加到 PATH（可选）
echo 'export PATH="/opt/homebrew/opt/mysql@5.7/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

#### 方法2：官方安装包
1. 访问 [MySQL官网](https://dev.mysql.com/downloads/mysql/5.7.html)
2. 下载 macOS 版本的 MySQL 5.7
3. 运行安装包并按提示安装
4. 在系统偏好设置中启动 MySQL

### Windows 安装

#### 方法1：MySQL Installer（推荐）
1. 访问 [MySQL官网](https://dev.mysql.com/downloads/installer/)
2. 下载 MySQL Installer for Windows
3. 运行安装程序，选择 "Developer Default" 或 "Server only"
4. 选择 MySQL Server 5.7 版本
5. 按提示完成安装和配置

#### 方法2：ZIP包安装
1. 下载 MySQL 5.7 ZIP 包
2. 解压到指定目录（如 C:\mysql）
3. 创建配置文件 my.ini
4. 初始化数据库并启动服务

### Ubuntu/Debian 安装

```bash
# 更新包列表
sudo apt update

# 安装 MySQL 5.7
sudo apt install mysql-server-5.7

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置（可选）
sudo mysql_secure_installation
```

### CentOS/RHEL 安装

```bash
# 添加 MySQL 5.7 仓库
sudo yum install https://dev.mysql.com/get/mysql57-community-release-el7-11.noarch.rpm

# 安装 MySQL 5.7
sudo yum install mysql-community-server

# 启动 MySQL 服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log
```

## 🔧 MySQL配置

### 1. 连接到MySQL

```bash
# 使用root用户连接
mysql -u root -p

# 如果是首次安装且没有密码
mysql -u root
```

### 2. 创建数据库用户（推荐）

```sql
-- 创建专用用户
CREATE USER 'comic_user'@'localhost' IDENTIFIED BY 'comic_password_123';

-- 授予权限
GRANT ALL PRIVILEGES ON comic_website.* TO 'comic_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 3. 修改环境配置

编辑 `comic-backend/.env` 文件：

```env
# 数据库配置 (MySQL 5.7)
DB_HOST=localhost
DB_PORT=3306
DB_USER=comic_user
DB_PASSWORD=comic_password_123
DB_NAME=comic_website
```

## 🏗️ 数据库结构初始化

### 自动初始化（推荐）

```bash
# 进入后端目录
cd comic-backend

# 运行初始化脚本
npm run init-db

# 或者直接运行
node scripts/init-database.js
```

### 手动初始化

如果自动初始化失败，可以手动执行：

```bash
# 连接到MySQL
mysql -u comic_user -p

# 创建数据库
CREATE DATABASE IF NOT EXISTS comic_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 使用数据库
USE comic_website;

# 执行建表语句（见下方SQL脚本）
```

### 数据库表结构

#### 1. 漫画表 (comics)
```sql
CREATE TABLE comics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '漫画标题',
  description TEXT COMMENT '漫画描述',
  author VARCHAR(100) COMMENT '作者',
  status ENUM('ongoing', 'completed', 'paused') DEFAULT 'ongoing' COMMENT '连载状态',
  category VARCHAR(50) COMMENT '分类',
  tags JSON COMMENT '标签',
  cover_image VARCHAR(500) COMMENT '封面图片路径',
  thumbnail_image VARCHAR(500) COMMENT '缩略图路径',
  total_chapters INT DEFAULT 0 COMMENT '总章节数',
  total_views INT DEFAULT 0 COMMENT '总浏览量',
  rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
  is_published BOOLEAN DEFAULT TRUE COMMENT '是否发布',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_title (title),
  INDEX idx_author (author),
  INDEX idx_status (status),
  INDEX idx_category (category),
  INDEX idx_created_at (created_at),
  INDEX idx_featured (is_featured),
  INDEX idx_published (is_published)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='漫画表';
```

#### 2. 章节表 (chapters)
```sql
CREATE TABLE chapters (
  id INT PRIMARY KEY AUTO_INCREMENT,
  comic_id INT NOT NULL COMMENT '漫画ID',
  chapter_number INT NOT NULL COMMENT '章节号',
  title VARCHAR(255) NOT NULL COMMENT '章节标题',
  description TEXT COMMENT '章节描述',
  total_pages INT DEFAULT 0 COMMENT '总页数',
  views INT DEFAULT 0 COMMENT '浏览量',
  is_published BOOLEAN DEFAULT TRUE COMMENT '是否发布',
  publish_date TIMESTAMP NULL COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (comic_id) REFERENCES comics(id) ON DELETE CASCADE,
  UNIQUE KEY unique_comic_chapter (comic_id, chapter_number),
  INDEX idx_comic_id (comic_id),
  INDEX idx_chapter_number (chapter_number),
  INDEX idx_publish_date (publish_date),
  INDEX idx_published (is_published)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='章节表';
```

#### 3. 页面表 (pages)
```sql
CREATE TABLE pages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  chapter_id INT NOT NULL COMMENT '章节ID',
  page_number INT NOT NULL COMMENT '页码',
  image_path VARCHAR(500) NOT NULL COMMENT '图片路径',
  thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
  image_width INT COMMENT '图片宽度',
  image_height INT COMMENT '图片高度',
  file_size INT COMMENT '文件大小(字节)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
  UNIQUE KEY unique_chapter_page (chapter_id, page_number),
  INDEX idx_chapter_id (chapter_id),
  INDEX idx_page_number (page_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面表';
```

#### 4. 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  role ENUM('super_admin', 'admin', 'editor') DEFAULT 'editor' COMMENT '角色',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';
```

#### 5. 文件上传记录表 (file_uploads)
```sql
CREATE TABLE file_uploads (
  id INT PRIMARY KEY AUTO_INCREMENT,
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  filename VARCHAR(255) NOT NULL COMMENT '存储文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size INT NOT NULL COMMENT '文件大小',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  upload_type ENUM('comic_cover', 'chapter_page', 'thumbnail', 'other') NOT NULL COMMENT '上传类型',
  related_id INT COMMENT '关联ID',
  uploaded_by INT COMMENT '上传者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_filename (filename),
  INDEX idx_upload_type (upload_type),
  INDEX idx_related_id (related_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表';
```

## 🌱 示例数据插入

### 自动插入示例数据

```bash
# 运行示例数据脚本
npm run seed

# 或者直接运行
node scripts/seed-data.js
```

### 验证数据插入

```sql
-- 检查数据
USE comic_website;

-- 查看漫画数量
SELECT COUNT(*) as comic_count FROM comics;

-- 查看章节数量
SELECT COUNT(*) as chapter_count FROM chapters;

-- 查看页面数量
SELECT COUNT(*) as page_count FROM pages;

-- 查看漫画列表
SELECT id, title, author, status, total_chapters FROM comics;
```

## 🔍 数据库连接测试

### 使用Node.js测试连接

```bash
# 在后端目录运行
cd comic-backend

# 测试数据库连接
node -e "
const { testConnection } = require('./config/database');
testConnection().then(result => {
  console.log('数据库连接测试:', result ? '成功' : '失败');
  process.exit(result ? 0 : 1);
});
"
```

### 使用命令行测试

```bash
# 测试连接
mysql -h localhost -P 3306 -u comic_user -p comic_website -e "SELECT 'Connection successful' as status;"
```

## 🚨 常见问题解决

### 1. 连接被拒绝
```bash
# 检查MySQL服务状态
# macOS
brew services list | grep mysql

# Linux
sudo systemctl status mysql

# 启动MySQL服务
# macOS
brew services start mysql@5.7

# Linux
sudo systemctl start mysql
```

### 2. 密码错误
```bash
# 重置root密码（MySQL 5.7）
sudo mysqld_safe --skip-grant-tables &
mysql -u root
```

```sql
-- 在MySQL中执行
USE mysql;
UPDATE user SET authentication_string = PASSWORD('new_password') WHERE User = 'root';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 字符集问题
```sql
-- 检查字符集
SHOW VARIABLES LIKE 'character_set%';

-- 修改字符集
ALTER DATABASE comic_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 权限问题
```sql
-- 检查用户权限
SHOW GRANTS FOR 'comic_user'@'localhost';

-- 重新授权
GRANT ALL PRIVILEGES ON comic_website.* TO 'comic_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📊 数据库维护

### 备份数据库
```bash
# 备份整个数据库
mysqldump -u comic_user -p comic_website > comic_website_backup.sql

# 备份特定表
mysqldump -u comic_user -p comic_website comics chapters > comics_backup.sql
```

### 恢复数据库
```bash
# 恢复数据库
mysql -u comic_user -p comic_website < comic_website_backup.sql
```

### 清理数据
```sql
-- 清空所有数据（保留表结构）
TRUNCATE TABLE pages;
TRUNCATE TABLE chapters;
TRUNCATE TABLE comics;
TRUNCATE TABLE file_uploads;
```

## ✅ 初始化检查清单

- [ ] MySQL 5.7 已安装并运行
- [ ] 数据库用户已创建并授权
- [ ] 环境变量配置正确
- [ ] 数据库结构初始化成功
- [ ] 示例数据插入成功
- [ ] 数据库连接测试通过
- [ ] 后端服务可以正常连接数据库

---

**🎉 数据库初始化完成！现在可以启动漫画网站系统了。**
