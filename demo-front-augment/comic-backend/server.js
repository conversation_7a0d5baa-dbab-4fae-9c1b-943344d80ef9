const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// 导入模块
const { testConnection } = require('./config/database');
const routes = require('./routes');
const {
  handleMulterError,
  handleDatabaseError,
  handleValidationError,
  handleJSONError,
  handle404,
  handleGenericError,
  requestLogger,
  securityHeaders
} = require('./middleware/errorHandler');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(compression());
app.use(securityHeaders);

// CORS配置
const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'http://localhost:8083',
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
app.use(cors(corsOptions));

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
if (process.env.NODE_ENV === 'development') {
  app.use(requestLogger);
} else {
  app.use(morgan('combined'));
}

// 限流中间件
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
    error: '已超出速率限制'
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use('/api/', limiter);

// 静态文件服务
const uploadsPath = process.env.UPLOAD_PATH || './uploads';
app.use('/uploads', express.static(path.resolve(uploadsPath), {
  maxAge: '1d', // 缓存1天
  etag: true,
  lastModified: true
}));

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🎉 漫画网站后端API服务',
    data: {
      name: '漫画管理系统API',
      version: '1.0.0',
      description: '基于Node.js + Express + MySQL 5.7的漫画图片资源管理平台',
      features: [
        '✅ RESTful API 架构',
        '✅ 漫画管理接口 (CRUD)',
        '✅ 章节管理接口',
        '✅ 文件上传功能 (单文件、多文件、批量)',
        '✅ 静态文件服务',
        '✅ CORS 跨域支持',
        '✅ 错误处理中间件',
        '✅ 数据库模型设计 (MySQL)',
        '✅ 图片处理 (压缩、缩略图)',
        '✅ 数据验证',
        '✅ 安全防护',
        '✅ 请求限流'
      ],
      endpoints: {
        health: '/api/health',
        info: '/api/info',
        comics: '/api/comics',
        chapters: '/api/chapters',
        pages: '/api/pages',
        upload: '/api/upload'
      },
      documentation: {
        swagger: '/api/docs',
        postman: '/api/postman'
      }
    },
    timestamp: new Date().toISOString()
  });
});

// API路由
app.use('/api', routes);

// JSON解析错误处理
app.use(handleJSONError);

// 错误处理中间件（按顺序）
app.use(handleMulterError);
app.use(handleDatabaseError);
app.use(handleValidationError);

// 404处理
app.use(handle404);

// 通用错误处理（必须放在最后）
app.use(handleGenericError);

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    console.log('🔗 正在测试数据库连接...');
    const dbConnected = await testConnection();

    if (!dbConnected) {
      console.warn('⚠️  数据库连接失败，但服务器将继续启动（仅限演示模式）');
      console.warn('⚠️  数据库相关功能将不可用');
    }

    // 确保上传目录存在
    const fs = require('fs').promises;
    try {
      await fs.access(uploadsPath);
    } catch (error) {
      await fs.mkdir(uploadsPath, { recursive: true });
      console.log(`📁 创建上传目录: ${uploadsPath}`);
    }

    // 启动HTTP服务器
    const server = app.listen(PORT, () => {
      console.log('🚀 漫画后端API服务启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 API文档: http://localhost:${PORT}/api/info`);
      console.log(`💾 数据库: MySQL 5.7`);
      console.log(`📁 上传目录: ${uploadsPath}`);
      console.log(`🔒 CORS允许: ${process.env.CORS_ORIGIN || 'http://localhost:8083'}`);
      console.log('✨ 服务器就绪，等待请求...\n');
    });

    // 设置服务器超时
    server.timeout = 30000; // 30秒

    return server;
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = app;
