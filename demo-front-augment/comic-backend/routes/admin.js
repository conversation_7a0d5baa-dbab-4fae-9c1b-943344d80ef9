const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/adminController');

// 简单的管理员验证中间件
const adminAuth = (req, res, next) => {
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
  const providedPassword = req.query.password || req.body.password || req.headers['x-admin-password'];
  
  // 如果没有设置密码或密码正确，则允许访问
  if (!adminPassword || providedPassword === adminPassword) {
    next();
  } else {
    // 返回简单的认证页面
    const authHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .login-box h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #4facfe;
        }
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <h1>🔐 管理员登录</h1>
        <form method="GET">
            <div class="form-group">
                <label for="password">管理员密码</label>
                <input type="password" id="password" name="password" class="form-control" placeholder="请输入管理员密码" required>
            </div>
            <button type="submit" class="btn">登录</button>
            ${req.query.password ? '<div class="error">密码错误，请重试</div>' : ''}
        </form>
        <div style="margin-top: 20px; font-size: 12px; color: #666;">
            默认密码: admin123 (可通过环境变量 ADMIN_PASSWORD 修改)
        </div>
    </div>
</body>
</html>`;
    
    res.send(authHtml);
  }
};

// 管理员上传页面
router.get('/', adminAuth, AdminController.showUploadPage);

// 获取统计信息
router.get('/stats', adminAuth, AdminController.getStats);

// 查看文件列表
router.get('/files', adminAuth, AdminController.getFileList);

// 清理临时文件
router.post('/cleanup', adminAuth, AdminController.cleanupFiles);

module.exports = router;
