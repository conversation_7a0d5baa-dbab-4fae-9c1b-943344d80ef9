const express = require('express');
const { body } = require('express-validator');
const UploadController = require('../controllers/uploadController');
const { UploadMiddleware } = require('../utils/upload');

const router = express.Router();

// 单文件上传
// POST /api/upload/single
router.post('/single',
  UploadMiddleware.single('file', 'general'),
  [
    body('generateThumbnail')
      .optional()
      .isBoolean()
      .withMessage('生成缩略图选项必须是布尔值'),
    body('compress')
      .optional()
      .isBoolean()
      .withMessage('压缩选项必须是布尔值'),
    body('related_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('关联ID必须是正整数')
  ],
  UploadController.uploadSingle
);

// 多文件上传
// POST /api/upload/multiple
router.post('/multiple',
  UploadMiddleware.array('files', 20, 'general'),
  [
    body('generateThumbnail')
      .optional()
      .isBoolean()
      .withMessage('生成缩略图选项必须是布尔值'),
    body('compress')
      .optional()
      .isBoolean()
      .withMessage('压缩选项必须是布尔值'),
    body('related_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('关联ID必须是正整数')
  ],
  UploadController.uploadMultiple
);

// 漫画封面上传
// POST /api/upload/comic-cover
router.post('/comic-cover',
  UploadMiddleware.single('cover', 'comic_cover'),
  [
    body('related_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('漫画ID必须是正整数')
  ],
  UploadController.uploadSingle
);

// 章节页面批量上传
// POST /api/upload/chapter-pages
router.post('/chapter-pages',
  UploadMiddleware.array('pages', 50, 'chapter_page'),
  [
    body('chapter_id')
      .notEmpty()
      .withMessage('章节ID不能为空')
      .isInt({ min: 1 })
      .withMessage('章节ID必须是正整数')
  ],
  UploadController.uploadMultiple
);

// 管理员批量上传 (用于管理后台)
// POST /api/upload/batch
router.post('/batch',
  UploadMiddleware.array('images', 50, 'general'),
  [
    body('comicTitle')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('漫画标题长度必须在1-100字符之间'),
    body('chapterNumber')
      .optional()
      .isInt({ min: 1 })
      .withMessage('章节号必须是正整数')
  ],
  UploadController.uploadMultiple
);

// 获取上传记录
// GET /api/upload/records
router.get('/records', UploadController.getUploadRecords);

// 删除上传的文件
// DELETE /api/upload/:id
router.delete('/:id', UploadController.deleteUploadedFile);

// 生成缩略图
// POST /api/upload/:id/thumbnail
router.post('/:id/thumbnail',
  [
    body('width')
      .optional()
      .isInt({ min: 50, max: 1000 })
      .withMessage('宽度必须在50-1000之间'),
    body('height')
      .optional()
      .isInt({ min: 50, max: 1000 })
      .withMessage('高度必须在50-1000之间'),
    body('quality')
      .optional()
      .isInt({ min: 10, max: 100 })
      .withMessage('质量必须在10-100之间')
  ],
  UploadController.generateThumbnail
);

// 获取图片信息
// GET /api/upload/:id/info
router.get('/:id/info', UploadController.getImageInfo);

module.exports = router;
