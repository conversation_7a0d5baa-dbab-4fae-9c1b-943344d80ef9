const express = require('express');
const comicsRouter = require('./comics');
const chaptersRouter = require('./chapters');
const pagesRouter = require('./pages');
const uploadRouter = require('./upload');
const adminRouter = require('./admin');

const router = express.Router();

// API 健康检查
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '漫画后端API服务正常运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API 信息
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: '漫画网站后端API',
      version: '1.0.0',
      description: '基于Node.js + Express + MySQL的漫画管理系统',
      author: 'Comic Website Team',
      endpoints: {
        comics: '/api/comics',
        chapters: '/api/chapters',
        pages: '/api/pages',
        upload: '/api/upload',
        admin: '/api/admin'
      },
      features: [
        '漫画CRUD操作',
        '章节管理',
        '页面管理',
        '文件上传',
        '图片处理',
        'RESTful API',
        'MySQL数据库',
        'CORS支持',
        '错误处理',
        '数据验证'
      ]
    },
    message: 'API信息获取成功'
  });
});

// 挂载子路由
router.use('/comics', comicsRouter);
router.use('/chapters', chaptersRouter);
router.use('/pages', pagesRouter);
router.use('/upload', uploadRouter);
router.use('/admin', adminRouter);

// 404 处理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `API端点不存在: ${req.method} ${req.originalUrl}`,
    available_endpoints: {
      'GET /api/health': '健康检查',
      'GET /api/info': 'API信息',
      'GET /api/comics': '获取漫画列表',
      'POST /api/comics': '创建漫画',
      'GET /api/comics/:id': '获取漫画详情',
      'PUT /api/comics/:id': '更新漫画',
      'DELETE /api/comics/:id': '删除漫画',
      'GET /api/chapters/:id': '获取章节详情',
      'POST /api/chapters': '创建章节',
      'PUT /api/chapters/:id': '更新章节',
      'DELETE /api/chapters/:id': '删除章节',
      'GET /api/pages/:id': '获取页面详情',
      'POST /api/pages': '创建页面',
      'POST /api/pages/batch': '批量创建页面',
      'PUT /api/pages/:id': '更新页面',
      'DELETE /api/pages/:id': '删除页面',
      'POST /api/upload/single': '单文件上传',
      'POST /api/upload/multiple': '多文件上传',
      'GET /api/upload/records': '获取上传记录',
      'GET /api/admin': '管理员上传页面',
      'GET /api/admin/stats': '获取统计信息',
      'GET /api/admin/files': '查看文件列表',
      'POST /api/admin/cleanup': '清理临时文件'
    }
  });
});

module.exports = router;
