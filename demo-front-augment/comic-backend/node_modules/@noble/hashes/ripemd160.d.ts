/**
 * RIPEMD-160 legacy hash function.
 * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html
 * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf
 * @module
 * @deprecated
 */
import { RIPEMD160 as RIPEMD160n, ripemd160 as ripemd160n } from './legacy.ts';
/** @deprecated Use import from `noble/hashes/legacy` module */
export declare const RIPEMD160: typeof RIPEMD160n;
/** @deprecated Use import from `noble/hashes/legacy` module */
export declare const ripemd160: typeof ripemd160n;
//# sourceMappingURL=ripemd160.d.ts.map