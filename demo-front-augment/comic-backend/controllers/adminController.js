const path = require('path');
const fs = require('fs').promises;
const UploadHelper = require('../utils/upload');

class AdminController {
  // 显示管理员上传页面
  static async showUploadPage(req, res) {
    try {
      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>漫画管理后台 - 图片上传</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }
        
        .upload-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .file-input {
            border: 2px dashed #4facfe;
            background: #f0f8ff;
            padding: 20px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-input:hover {
            background: #e6f3ff;
        }
        
        .file-input input[type="file"] {
            display: none;
        }
        
        .file-input-text {
            color: #4facfe;
            font-size: 16px;
            font-weight: 500;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .message {
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-list {
            margin-top: 20px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        
        .file-info {
            display: flex;
            align-items: center;
        }
        
        .file-icon {
            width: 40px;
            height: 40px;
            background: #4facfe;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .file-details h4 {
            margin: 0;
            color: #333;
        }
        
        .file-details p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 漫画管理后台</h1>
            <p>图片资源上传与管理系统</p>
        </div>
        
        <div class="main-content">
            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalFiles">0</div>
                    <div class="stat-label">总文件数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalSize">0 MB</div>
                    <div class="stat-label">总大小</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayUploads">0</div>
                    <div class="stat-label">今日上传</div>
                </div>
            </div>
            
            <!-- 单文件上传 -->
            <div class="upload-section">
                <h2>📁 单文件上传</h2>
                <form id="singleUploadForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="comicTitle">漫画标题</label>
                        <input type="text" id="comicTitle" name="comicTitle" class="form-control" placeholder="请输入漫画标题">
                    </div>
                    
                    <div class="form-group">
                        <label for="chapterNumber">章节号</label>
                        <input type="number" id="chapterNumber" name="chapterNumber" class="form-control" placeholder="请输入章节号">
                    </div>
                    
                    <div class="form-group">
                        <label>选择图片文件</label>
                        <div class="file-input" onclick="document.getElementById('singleFile').click()">
                            <input type="file" id="singleFile" name="image" accept="image/*">
                            <div class="file-input-text">
                                📷 点击选择图片文件<br>
                                <small>支持 JPG, PNG, GIF 格式，最大 10MB</small>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">上传文件</button>
                </form>
            </div>
            
            <!-- 批量上传 -->
            <div class="upload-section">
                <h2>📚 批量上传</h2>
                <form id="batchUploadForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="batchComicTitle">漫画标题</label>
                        <input type="text" id="batchComicTitle" name="comicTitle" class="form-control" placeholder="请输入漫画标题">
                    </div>
                    
                    <div class="form-group">
                        <label for="batchChapterNumber">章节号</label>
                        <input type="number" id="batchChapterNumber" name="chapterNumber" class="form-control" placeholder="请输入章节号">
                    </div>
                    
                    <div class="form-group">
                        <label>选择多个图片文件</label>
                        <div class="file-input" onclick="document.getElementById('batchFiles').click()">
                            <input type="file" id="batchFiles" name="images" accept="image/*" multiple>
                            <div class="file-input-text">
                                📷 点击选择多个图片文件<br>
                                <small>支持同时选择多个文件进行批量上传</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="file-list" id="fileList"></div>
                    
                    <button type="submit" class="btn">批量上传</button>
                    <button type="button" class="btn btn-secondary" onclick="clearFileList()">清空列表</button>
                </form>
            </div>
            
            <!-- 进度条 -->
            <div class="progress" id="uploadProgress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <!-- 消息提示 -->
            <div class="message" id="message"></div>
            
            <!-- 管理操作 -->
            <div class="upload-section">
                <h2>🛠️ 管理操作</h2>
                <button type="button" class="btn btn-secondary" onclick="refreshStats()">刷新统计</button>
                <button type="button" class="btn btn-secondary" onclick="viewFiles()">查看文件</button>
                <button type="button" class="btn btn-danger" onclick="cleanupFiles()">清理临时文件</button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计信息
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
        });

        // 单文件上传
        document.getElementById('singleUploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const fileInput = document.getElementById('singleFile');
            
            if (!fileInput.files[0]) {
                showMessage('请选择要上传的文件', 'error');
                return;
            }
            
            try {
                showProgress(true);
                const response = await uploadFile('/api/upload/single', formData);
                
                if (response.success) {
                    showMessage('文件上传成功！', 'success');
                    this.reset();
                    refreshStats();
                } else {
                    showMessage('上传失败: ' + response.message, 'error');
                }
            } catch (error) {
                showMessage('上传失败: ' + error.message, 'error');
            } finally {
                showProgress(false);
            }
        });

        // 批量上传
        document.getElementById('batchUploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const fileInput = document.getElementById('batchFiles');
            
            if (!fileInput.files.length) {
                showMessage('请选择要上传的文件', 'error');
                return;
            }
            
            try {
                showProgress(true);
                const response = await uploadFile('/api/upload/batch', formData);
                
                if (response.success) {
                    showMessage(\`批量上传成功！共上传 \${response.data.uploaded} 个文件\`, 'success');
                    this.reset();
                    clearFileList();
                    refreshStats();
                } else {
                    showMessage('上传失败: ' + response.message, 'error');
                }
            } catch (error) {
                showMessage('上传失败: ' + error.message, 'error');
            } finally {
                showProgress(false);
            }
        });

        // 文件选择事件
        document.getElementById('singleFile').addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const fileInputText = this.parentElement.querySelector('.file-input-text');
                fileInputText.innerHTML = \`📷 已选择: \${file.name}<br><small>\${formatFileSize(file.size)}</small>\`;
            }
        });

        document.getElementById('batchFiles').addEventListener('change', function() {
            updateFileList(this.files);
        });

        // 更新文件列表
        function updateFileList(files) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            Array.from(files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = \`
                    <div class="file-info">
                        <div class="file-icon">IMG</div>
                        <div class="file-details">
                            <h4>\${file.name}</h4>
                            <p>\${formatFileSize(file.size)} • \${file.type}</p>
                        </div>
                    </div>
                    <button type="button" class="btn btn-danger" onclick="removeFile(\${index})">删除</button>
                \`;
                fileList.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            const fileInput = document.getElementById('batchFiles');
            const dt = new DataTransfer();
            
            Array.from(fileInput.files).forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            fileInput.files = dt.files;
            updateFileList(fileInput.files);
        }

        // 清空文件列表
        function clearFileList() {
            document.getElementById('batchFiles').value = '';
            document.getElementById('fileList').innerHTML = '';
        }

        // 上传文件
        async function uploadFile(url, formData) {
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });
            
            return await response.json();
        }

        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = \`message \${type}\`;
            message.style.display = 'block';
            
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        // 显示/隐藏进度条
        function showProgress(show) {
            const progress = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            
            if (show) {
                progress.style.display = 'block';
                progressBar.style.width = '0%';
                
                // 模拟进度
                let width = 0;
                const interval = setInterval(() => {
                    width += Math.random() * 30;
                    if (width >= 90) {
                        clearInterval(interval);
                        width = 90;
                    }
                    progressBar.style.width = width + '%';
                }, 200);
                
                progress.interval = interval;
            } else {
                if (progress.interval) {
                    clearInterval(progress.interval);
                }
                progressBar.style.width = '100%';
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 500);
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 刷新统计信息
        async function refreshStats() {
            try {
                const response = await fetch('/api/admin/stats');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('totalFiles').textContent = data.data.totalFiles;
                    document.getElementById('totalSize').textContent = formatFileSize(data.data.totalSize);
                    document.getElementById('todayUploads').textContent = data.data.todayUploads;
                }
            } catch (error) {
                console.error('获取统计信息失败:', error);
            }
        }

        // 查看文件
        function viewFiles() {
            window.open('/api/admin/files', '_blank');
        }

        // 清理临时文件
        async function cleanupFiles() {
            if (!confirm('确定要清理临时文件吗？此操作不可恢复。')) {
                return;
            }
            
            try {
                const response = await fetch('/api/admin/cleanup', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('清理完成！', 'success');
                    refreshStats();
                } else {
                    showMessage('清理失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('清理失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>`;

      res.send(html);
    } catch (error) {
      console.error('显示上传页面失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: error.message
      });
    }
  }

  // 获取统计信息
  static async getStats(req, res) {
    try {
      const uploadsPath = process.env.UPLOAD_PATH || './uploads';
      
      // 获取上传目录信息
      let totalFiles = 0;
      let totalSize = 0;
      let todayUploads = 0;
      
      try {
        const files = await fs.readdir(uploadsPath, { withFileTypes: true });
        const today = new Date().toDateString();
        
        for (const file of files) {
          if (file.isFile()) {
            const filePath = path.join(uploadsPath, file.name);
            const stats = await fs.stat(filePath);
            
            totalFiles++;
            totalSize += stats.size;
            
            // 检查是否是今天上传的
            if (stats.birthtime.toDateString() === today) {
              todayUploads++;
            }
          }
        }
      } catch (error) {
        console.log('读取上传目录失败:', error.message);
      }

      res.json({
        success: true,
        data: {
          totalFiles,
          totalSize,
          todayUploads
        }
      });
    } catch (error) {
      console.error('获取统计信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败',
        error: error.message
      });
    }
  }

  // 查看文件列表
  static async getFileList(req, res) {
    try {
      const uploadsPath = process.env.UPLOAD_PATH || './uploads';
      const files = [];
      
      try {
        const fileList = await fs.readdir(uploadsPath, { withFileTypes: true });
        
        for (const file of fileList) {
          if (file.isFile()) {
            const filePath = path.join(uploadsPath, file.name);
            const stats = await fs.stat(filePath);
            
            files.push({
              name: file.name,
              size: stats.size,
              created: stats.birthtime,
              modified: stats.mtime,
              url: `/uploads/${file.name}`
            });
          }
        }
      } catch (error) {
        console.log('读取文件列表失败:', error.message);
      }

      // 按创建时间倒序排列
      files.sort((a, b) => new Date(b.created) - new Date(a.created));

      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件列表 - 漫画管理后台</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .file-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .file-item { border: 1px solid #ddd; border-radius: 8px; padding: 15px; text-align: center; }
        .file-item img { max-width: 100%; height: 150px; object-fit: cover; border-radius: 4px; }
        .file-info { margin-top: 10px; font-size: 12px; color: #666; }
        .file-name { font-weight: bold; margin-bottom: 5px; word-break: break-all; }
        h1 { color: #333; margin-bottom: 30px; }
        .stats { background: #f5f5f5; padding: 15px; border-radius: 8px; margin-bottom: 30px; }
    </style>
</head>
<body>
    <h1>📁 文件列表 (${files.length} 个文件)</h1>
    
    <div class="stats">
        <strong>总文件数:</strong> ${files.length} | 
        <strong>总大小:</strong> ${(files.reduce((sum, f) => sum + f.size, 0) / 1024 / 1024).toFixed(2)} MB
    </div>
    
    <div class="file-grid">
        ${files.map(file => `
          <div class="file-item">
            <img src="${file.url}" alt="${file.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiM5OTkiPk5PIElNQUdFPC90ZXh0Pjwvc3ZnPg=='">
            <div class="file-name">${file.name}</div>
            <div class="file-info">
              大小: ${(file.size / 1024).toFixed(1)} KB<br>
              创建: ${new Date(file.created).toLocaleString()}
            </div>
          </div>
        `).join('')}
    </div>
</body>
</html>`;

      res.send(html);
    } catch (error) {
      console.error('获取文件列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取文件列表失败',
        error: error.message
      });
    }
  }

  // 清理临时文件
  static async cleanupFiles(req, res) {
    try {
      const uploadsPath = process.env.UPLOAD_PATH || './uploads';
      let deletedCount = 0;
      
      try {
        const files = await fs.readdir(uploadsPath, { withFileTypes: true });
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        
        for (const file of files) {
          if (file.isFile() && file.name.startsWith('temp_')) {
            const filePath = path.join(uploadsPath, file.name);
            const stats = await fs.stat(filePath);
            
            // 删除超过1天的临时文件
            if (stats.birthtime < oneDayAgo) {
              await fs.unlink(filePath);
              deletedCount++;
            }
          }
        }
      } catch (error) {
        console.log('清理文件失败:', error.message);
      }

      res.json({
        success: true,
        message: `清理完成，删除了 ${deletedCount} 个临时文件`,
        data: { deletedCount }
      });
    } catch (error) {
      console.error('清理文件失败:', error);
      res.status(500).json({
        success: false,
        message: '清理文件失败',
        error: error.message
      });
    }
  }
}

module.exports = AdminController;
