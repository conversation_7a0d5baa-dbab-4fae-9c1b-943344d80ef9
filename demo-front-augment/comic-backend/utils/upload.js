const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
// const sharp = require('sharp'); // 暂时注释掉，避免安装问题
const { v4: uuidv4 } = require('uuid');

// 确保上传目录存在
async function ensureUploadDir(dir) {
  try {
    await fs.access(dir);
  } catch (error) {
    await fs.mkdir(dir, { recursive: true });
  }
}

// 生成唯一文件名
function generateFileName(originalName) {
  const ext = path.extname(originalName).toLowerCase();
  const name = uuidv4();
  return `${name}${ext}`;
}

// 检查文件类型
function checkFileType(file, allowedTypes) {
  const fileTypes = allowedTypes || process.env.ALLOWED_FILE_TYPES?.split(',') || ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  const extname = path.extname(file.originalname).toLowerCase().substring(1);
  const mimetype = file.mimetype;

  const validExtensions = fileTypes.includes(extname);
  const validMimetypes = mimetype.startsWith('image/');

  return validExtensions && validMimetypes;
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: async function (req, file, cb) {
    const uploadPath = process.env.UPLOAD_PATH || './uploads';
    const subDir = req.uploadType || 'general';
    const fullPath = path.join(uploadPath, subDir);

    try {
      await ensureUploadDir(fullPath);
      cb(null, fullPath);
    } catch (error) {
      cb(error);
    }
  },
  filename: function (req, file, cb) {
    const fileName = generateFileName(file.originalname);
    cb(null, fileName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  if (checkFileType(file)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 创建multer实例
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    files: 50 // 最多50个文件
  },
  fileFilter: fileFilter
});

// 图片处理工具
class ImageProcessor {
  // 生成缩略图 (暂时简化实现)
  static async generateThumbnail(inputPath, outputPath, options = {}) {
    const {
      width = parseInt(process.env.THUMBNAIL_WIDTH) || 200,
      height = parseInt(process.env.THUMBNAIL_HEIGHT) || 280,
      quality = parseInt(process.env.IMAGE_QUALITY) || 80
    } = options;

    try {
      // 暂时直接复制文件，后续可以添加 Sharp 处理
      await fs.copyFile(inputPath, outputPath);
      return outputPath;
    } catch (error) {
      console.error('生成缩略图失败:', error);
      throw error;
    }
  }

  // 压缩图片 (暂时简化实现)
  static async compressImage(inputPath, outputPath, options = {}) {
    const {
      quality = parseInt(process.env.IMAGE_QUALITY) || 80,
      maxWidth = 1200,
      maxHeight = 1600
    } = options;

    try {
      // 暂时直接复制文件，后续可以添加 Sharp 处理
      await fs.copyFile(inputPath, outputPath);
      return outputPath;
    } catch (error) {
      console.error('压缩图片失败:', error);
      throw error;
    }
  }

  // 获取图片信息 (暂时简化实现)
  static async getImageInfo(imagePath) {
    try {
      const stats = await fs.stat(imagePath);

      return {
        width: 800, // 默认值
        height: 1200, // 默认值
        format: path.extname(imagePath).slice(1),
        size: stats.size,
        channels: 3,
        hasAlpha: false
      };
    } catch (error) {
      console.error('获取图片信息失败:', error);
      throw error;
    }
  }
}

// 上传中间件工厂
class UploadMiddleware {
  // 单文件上传
  static single(fieldName, uploadType = 'general') {
    return (req, res, next) => {
      req.uploadType = uploadType;
      upload.single(fieldName)(req, res, next);
    };
  }

  // 多文件上传（同一字段）
  static array(fieldName, maxCount = 10, uploadType = 'general') {
    return (req, res, next) => {
      req.uploadType = uploadType;
      upload.array(fieldName, maxCount)(req, res, next);
    };
  }

  // 多文件上传（不同字段）
  static fields(fields, uploadType = 'general') {
    return (req, res, next) => {
      req.uploadType = uploadType;
      upload.fields(fields)(req, res, next);
    };
  }

  // 任意文件上传
  static any(uploadType = 'general') {
    return (req, res, next) => {
      req.uploadType = uploadType;
      upload.any()(req, res, next);
    };
  }
}

// 文件处理工具
class FileHandler {
  // 处理上传的文件
  static async processUploadedFile(file, options = {}) {
    const {
      generateThumbnail = false,
      compress = false,
      thumbnailOptions = {},
      compressOptions = {}
    } = options;

    const result = {
      original: {
        path: file.path,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      }
    };

    try {
      // 获取图片信息
      const imageInfo = await ImageProcessor.getImageInfo(file.path);
      result.original.width = imageInfo.width;
      result.original.height = imageInfo.height;

      // 生成缩略图
      if (generateThumbnail) {
        const thumbnailDir = path.join(path.dirname(file.path), 'thumbnails');
        await ensureUploadDir(thumbnailDir);

        const thumbnailPath = path.join(thumbnailDir, `thumb_${file.filename}`);
        await ImageProcessor.generateThumbnail(file.path, thumbnailPath, thumbnailOptions);

        result.thumbnail = {
          path: thumbnailPath,
          filename: `thumb_${file.filename}`
        };
      }

      // 压缩图片
      if (compress) {
        const compressedDir = path.join(path.dirname(file.path), 'compressed');
        await ensureUploadDir(compressedDir);

        const compressedPath = path.join(compressedDir, `comp_${file.filename}`);
        await ImageProcessor.compressImage(file.path, compressedPath, compressOptions);

        result.compressed = {
          path: compressedPath,
          filename: `comp_${file.filename}`
        };
      }

      return result;
    } catch (error) {
      console.error('处理上传文件失败:', error);
      throw error;
    }
  }

  // 批量处理上传的文件
  static async processUploadedFiles(files, options = {}) {
    const results = [];

    for (const file of files) {
      try {
        const result = await this.processUploadedFile(file, options);
        results.push(result);
      } catch (error) {
        console.error(`处理文件 ${file.originalname} 失败:`, error);
        // 继续处理其他文件
        results.push({
          error: error.message,
          file: file.originalname
        });
      }
    }

    return results;
  }

  // 删除文件
  static async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  }

  // 批量删除文件
  static async deleteFiles(filePaths) {
    const results = [];

    for (const filePath of filePaths) {
      const success = await this.deleteFile(filePath);
      results.push({ path: filePath, success });
    }

    return results;
  }
}

module.exports = {
  upload,
  UploadMiddleware,
  ImageProcessor,
  FileHandler,
  generateFileName,
  checkFileType,
  ensureUploadDir
};
